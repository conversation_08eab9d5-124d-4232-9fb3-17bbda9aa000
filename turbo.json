{"$schema": "https://turborepo.org/schema.json", "ui": "stream", "tasks": {"dev": {"cache": false, "persistent": true, "dependsOn": ["^build"]}, "build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "typecheck": {"dependsOn": ["^build"]}, "lint": {"dependsOn": ["^build", "^lint"]}, "lint:fix": {"dependsOn": ["^build", "^lint:fix"]}, "format": {"dependsOn": ["^format"]}, "format:fix": {"dependsOn": ["^format:fix"]}, "clean": {"cache": false}, "test:ci": {"dependsOn": ["^build", "^test:ci"]}, "check-and-build": {"dependsOn": ["build", "lint", "format", "test:ci"]}, "check": {"dependsOn": ["typecheck", "lint", "format", "test:ci"]}}, "globalEnv": ["ANALYZE"]}