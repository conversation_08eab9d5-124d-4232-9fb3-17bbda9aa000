name: Android - Build and Submit
on:
  workflow_call:
  workflow_dispatch:
jobs:
  build-ios:
    name: "[Android] - Build & submit"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: "10.10.0"
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          packager: pnpm

      - name: Install dependencies
        run: pnpm --filter '*' install --frozen-lockfile

      - name: Build on EAS
        id: eas-build
        working-directory: apps/mobile
        run: |
          BUILD_OUTPUT=$(eas build --platform android --profile production --non-interactive --json)
          echo "build_id=$(echo $BUILD_OUTPUT | jq -r '.[].id')" >> $GITHUB_OUTPUT

      - name: Submit on Google Play Store
        working-directory: apps/mobile
        run: eas submit --platform android --profile production --latest --track internal-testing --id ${{ steps.eas-build.outputs.build_id }}

      - name: Get build details from EAS
        id: get-build-version
        working-directory: apps/mobile
        run: |
          sleep 10
          BUILD_DETAILS=$(eas build:view ${{ steps.eas-build.outputs.build_id }} --json)
          VERSION_NUMBER=$(echo "$BUILD_DETAILS" | jq -r '.appVersion')
          BUILD_NUMBER=$(echo "$BUILD_DETAILS" | jq -r '.appBuildVersion')
          echo "version=$VERSION_NUMBER" >> $GITHUB_OUTPUT
          echo "build_number=$BUILD_NUMBER" >> $GITHUB_OUTPUT

      - name: Send Slack notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: "https://play.google.com/console/u/0/developers/7679128213141352722/app/4972302050567715906/tracks/internal-testing"
          MSG_MINIMAL: true
          SLACK_USERNAME: Pearl app
          SLACK_TITLE: 🤖 Android - Nouvelle version Test Interne dispo / ${{ steps.get-build-version.outputs.version }} / (Build ${{ steps.get-build-version.outputs.build_number }})
          SLACK_FOOTER: Android - Test Interne
