packages:
  - "apps/*"
  - "packages/*"
  - "tools/*"
# hoistPattern:
#   - "*"
#   - "!react"
#   - "!react-dom"
#   - "!@types/react"
#   - "!@types/react-dom"
# publicHoistPattern:
#   - "*expo-modules-core"
#   - "*metro-cache"
#   - "*expo-modules-autolinking"
#   - "*babel-preset-expo"
#   - "*react-native-css-interop*"
#   - "*@babel/plugin-transform-react-jsx*"
#   - "*@react-native/gradle-plugin*"
#   - "*import-in-the-middle*"
#   - "*require-in-the-middle*"
catalog:
  typescript: ~5.8.3
  "@tanstack/react-query": ^5.75.2
  prettier: ^3.5.3
  eslint: ^9.23.0
  "@types/node": ^22.15.29
catalogs:
  react19:
    react: 19.0.0
    react-dom: 19.0.0
    "@types/react": ^19.1.6
    "@types/react-dom": ^19.1.5
onlyBuiltDependencies:
  - esbuild
  - sharp