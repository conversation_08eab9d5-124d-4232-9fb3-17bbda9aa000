import eslintImports from "eslint-plugin-import";
import sortImports from "eslint-plugin-simple-import-sort";
import unusedImports from "eslint-plugin-unused-imports";
import tseslint from "typescript-eslint";

/** @type {Awaited<import('typescript-eslint').Config>} */
export default tseslint.config(
  eslintImports.configs.typescript,
  {
    plugins: {
      "simple-import-sort": sortImports,
      import: eslintImports,
      "unused-imports": unusedImports,
    },
    settings: {
      "import/resolver": {
        typescript: true,
        node: true,
      },
    },
    rules: {
      /*
       * Standardized import syntax
       */
      "@typescript-eslint/consistent-type-imports": [
        "error",
        { prefer: "type-imports", fixStyle: "separate-type-imports" },
      ], // use "import type" for types. Optimize import readability, bundle size, avoid circular dependencies
      "@typescript-eslint/no-require-imports": "error",
      "@typescript-eslint/no-var-requires": "off", // it's mostly a duplicate of no-require-imports

      /*
       * Auto-remove unused imports
       */
      "@typescript-eslint/no-unused-vars": "off", // eslint-plugin-unused-imports replaces this rule
      "unused-imports/no-unused-imports": "error", // Automatically remove unused imports on save
      "unused-imports/no-unused-vars": "warn",

      /*
       * Import sorting
       */
      "simple-import-sort/imports": [
        "error",
        {
          groups: [
            ["^\\u0000"], // side effect imports
            ["^@?\\w"], // packages
            ["^@pearl"], // internal packages
            ["^~"], // internal absolute imports
            ["^[^.]"], // everything else
            ["^\\."], // relative imports
          ],
        },
      ],
      "simple-import-sort/exports": "error",
      "import/first": "error",
      "import/newline-after-import": "error",
      "import/no-duplicates": "error",
    },
  },
  {
    files: ["*.js"],
    rules: {
      "@typescript-eslint/no-require-imports": "off",
    },
  },
);
