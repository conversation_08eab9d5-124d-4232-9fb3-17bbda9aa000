import { fixupPluginRules } from "@eslint/compat";
import pluginQuery from "@tanstack/eslint-plugin-query";
import reactPlugin from "eslint-plugin-react";
import hooksPlugin from "eslint-plugin-react-hooks";
import eslintReactNative from "eslint-plugin-react-native"; // Includes react-native, react and react hooks
import tseslint from "typescript-eslint";

/** @type {Awaited<import('typescript-eslint').Config>} */
export default tseslint.config(...pluginQuery.configs["flat/recommended"], {
  name: "eslint-plugin-react-native",
  plugins: {
    "react-native": fixupPluginRules({
      rules: eslintReactNative.rules,
    }),
    "react-hooks": hooksPlugin,
    react: reactPlugin,
  },
  settings: {
    react: {
      version: "detect",
    },
  },
  rules: {
    ...reactPlugin.configs["jsx-runtime"].rules, // Disables the rules that require importing react when using JSX
    ...hooksPlugin.configs.recommended.rules,
    ...eslintReactNative.configs.all.rules,
    "no-var": "error",
    eqeqeq: "error",
    "no-constant-binary-expression": "error",
    "no-else-return": "error",
    "require-await": "error",
    "no-nested-ternary": "error",
    "@typescript-eslint/ban-ts-comment": "warn",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/indent": "off",
    "@typescript-eslint/no-explicit-any": "error",
    "@typescript-eslint/no-non-null-assertion": "error",
    "@typescript-eslint/no-shadow": "error",
    "no-console": ["error", { allow: ["warn", "error"] }],
    "no-return-await": "error",
    "array-callback-return": "error",
    "react-hooks/exhaustive-deps": "error",
    "react-native/sort-styles": "off",
    "react-native/no-inline-styles": "off", // We do some tailwind anyway
    "react-native/no-raw-text": "off",
    "react/no-unstable-nested-components": "error",
    "react/prop-types": "off",
    "react/no-unused-prop-types": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "react-native/split-platform-components": "off", // Disable access with "if" Platform methods
    "@typescript-eslint/no-floating-promises": "off",
    "@typescript-eslint/no-unsafe-assignment": "off",
    "@typescript-eslint/no-redundant-type-constituents": "off",
    "@typescript-eslint/consistent-type-definitions": "off",
    "@typescript-eslint/non-nullable-type-assertion-style": "off",
    "react/jsx-no-useless-fragment": ["error", { allowExpressions: true }], // allows to have just a children inside a fragment
    "no-restricted-syntax": [
      "error",
      {
        selector:
          'ImportDeclaration[source.value="react-native-gesture-handler"] > ImportSpecifier[imported.name="ScrollView"]',
        message:
          "Avoid importing ScrollView from 'react-native-gesture-handler'",
      },
    ],
  },
});
