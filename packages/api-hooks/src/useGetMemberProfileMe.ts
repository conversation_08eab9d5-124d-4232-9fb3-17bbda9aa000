import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type MemberProfile = {
  id: string;
  userId: string;
  organizationId: string;
  isAdmin: boolean;
  firstName: string;
  lastName: string;
  email: string;
};

export const useGetMemberProfileMe = () => {
  const { apiClient } = useApiClient();

  return useQuery<MemberProfile>({
    queryKey: ["memberProfile"],
    queryFn: () =>
      apiClient.get<MemberProfile>(`/workspace/member_profiles/me`),
  });
};
