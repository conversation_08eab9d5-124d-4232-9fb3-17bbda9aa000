import {
  QueryClient,
  QueryClientProvider as _QueryClientProvider,
} from "@tanstack/react-query";
import camelcaseKeys from "camelcase-keys";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      select: (data: unknown) => {
        // Check if data is an object or array before transforming
        if (data && (typeof data === "object" || Array.isArray(data))) {
          return camelcaseKeys(
            data as
              | Record<string, unknown>
              | readonly Record<string, unknown>[],
            {
              deep: true,
            },
          );
        }
        return data; // Return unchanged if not an object/array
      },
    },
  },
});

export function QueryClientProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <_QueryClientProvider client={queryClient}>{children}</_QueryClientProvider>
  );
}
