import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

interface UpdateThreadNameParams {
  threadId: string;
  name: string;
}

export const useUpdateThreadName = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ threadId, name }: UpdateThreadNameParams) =>
      apiClient.put(`/agent/threads/${threadId}/name`, { name }),
    onSuccess: async (_, { threadId }) => {
      await queryClient.invalidateQueries({ queryKey: ["threads"] });
      await queryClient.invalidateQueries({ queryKey: ["thread", threadId] });
    },
    onError: (error) => {
      console.error("Failed to update thread name:", error);
    },
  });
};
