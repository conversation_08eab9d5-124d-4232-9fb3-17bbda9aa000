import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

interface UploadTeamDocumentResponse {
  id: string;
  title: string;
  format: string;
  dateUploaded: string;
  url: string;
}

interface UploadTeamDocumentParams {
  file: File;
}

export const useUploadTeamDocument = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file }: UploadTeamDocumentParams) =>
      apiClient.uploadFile<UploadTeamDocumentResponse>(
        "/workspace/admin/team-documents",
        file,
      ),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ["teamDocuments"] });
    },
  });
};
