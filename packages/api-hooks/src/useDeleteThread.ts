import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export const useDeleteThread = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (threadId: string) =>
      apiClient.delete(`/agent/threads/${threadId}`),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["threads"] }),
  });
};
