import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type Thread = {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
};

type ThreadsResponse = {
  threads: Thread[];
};

export const useGetThreads = () => {
  const { apiClient } = useApiClient();

  return useQuery<ThreadsResponse>({
    queryKey: ["threads"],
    queryFn: () => apiClient.get<ThreadsResponse>(`/agent/threads`),
  });
};
