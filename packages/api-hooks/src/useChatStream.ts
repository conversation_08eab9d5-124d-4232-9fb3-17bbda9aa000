import { useChat } from "@ai-sdk/react";
import type { ToolResult } from "ai";

import { useApiClient } from "@pearl/api-client";

interface UseChatStreamProps {
  threadId: string;
  crmAccountId?: string;
  onError: (error: Error) => void;
  onFinish?: () => void;
}

type ResultToolInvocation = {
  state: "result";
  step?: number;
} & ToolResult<string, object, object>;

export function useChatStream({
  threadId,
  crmAccountId,
  onError,
  onFinish,
}: UseChatStreamProps) {
  const { apiClient } = useApiClient();

  return useChat({
    id: threadId,
    api: "/agent/chat_stream",
    fetch: apiClient.authenticatedFetch.bind(apiClient),
    onError,
    experimental_prepareRequestBody: ({ messages }) => {
      const lastMessage = messages[messages.length - 1];
      // TODO: Maybe we should return undefined here ?
      if (!lastMessage) {
        return {
          content: "",
          role: "user",
          tool_calls: [],
          thread_id: threadId,
          crm_account_id: crmAccountId,
        };
      }
      const { content, role, parts } = lastMessage;
      return {
        content,
        role,
        tool_calls: parts
          .filter((part) => part.type === "tool-invocation")
          .filter((part) => part.toolInvocation.state === "result")
          .map((part) => ({
            tool_name: part.toolInvocation.toolName,
            result: (part.toolInvocation as unknown as ResultToolInvocation)
              .result,
          }))
          .slice(-1), // TODO: find a better way to handle multiple tool calls
        thread_id: threadId,
        crm_account_id: crmAccountId,
      };
    },
    onFinish: () => {
      onFinish?.();
    },
  });
}
