import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type AccountThread = {
  id: string;
  name: string;
  accountId: string;
  createdAt: string;
  updatedAt: string;
};

type AccountThreadsResponse = {
  threads: AccountThread[];
};

export const useGetAccountThreads = (accountId?: string) => {
  const { apiClient } = useApiClient();

  return useQuery<AccountThreadsResponse>({
    queryKey: ["accountThreads", accountId],
    queryFn: () =>
      apiClient.get<AccountThreadsResponse>(
        `/agent/accounts/${accountId}/threads`,
      ),
    enabled: !!accountId,
  });
};
