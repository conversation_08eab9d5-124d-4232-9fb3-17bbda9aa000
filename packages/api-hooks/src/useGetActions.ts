import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type Action = {
  title: string;
  description: string;
  prompt: string;
};

export const useGetActions = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["actions"],
    queryFn: async () => {
      const response = await apiClient.get<Action[]>(`/agent/actions`);
      return response;
    },
  });
};
