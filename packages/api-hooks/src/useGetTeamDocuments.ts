import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type TeamDocument = {
  id: string;
  title: string;
  format: string;
  dateUploaded: string;
};

export const useGetTeamDocuments = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["teamDocuments"],
    queryFn: async () => {
      const response = await apiClient.get<TeamDocument[]>(
        "/workspace/team-documents",
      );
      return response;
    },
  });
};
