{"name": "@pearl/api-hooks", "version": "0.1.0", "private": true, "type": "module", "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./query-client": {"types": "./dist/queryClient.d.ts", "default": "./src/queryClient.tsx"}}, "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc --watch", "lint": "eslint src/ --cache --cache-location .cache/.eslintcache", "lint:fix": "eslint src/ --fix --cache --cache-location .cache/.eslintcache", "format": "prettier . --check --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore", "format:fix": "prettier . --write --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@pearl/api-client": "workspace:*", "@tanstack/react-query": "^5.75.2", "ai": "^4.3.16", "camelcase-keys": "^9.1.3", "react": "catalog:react19"}, "devDependencies": {"@pearl/eslint-config": "workspace:*", "@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "@types/react": "catalog:react19", "eslint": "catalog:", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@pearl/prettier-config"}