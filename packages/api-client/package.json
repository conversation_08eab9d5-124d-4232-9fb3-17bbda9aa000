{"name": "@pearl/api-client", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc --watch", "lint": "eslint src/ --cache --cache-location .cache/.eslintcache", "lint:fix": "eslint src/ --fix --cache --cache-location .cache/.eslintcache", "format": "prettier . --check --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore", "format:fix": "prettier . --write --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore"}, "dependencies": {"react": "catalog:react19"}, "devDependencies": {"@pearl/eslint-config": "workspace:*", "@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "@types/react": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@pearl/prettier-config"}