"use client";

import type { ReactNode } from "react";
import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";

import { useApiClient } from "../context/ApiClient.context";

type AuthContextType = {
  userId: string | null;
  authenticated: boolean;
  loading: boolean;
  requestOtc: (email: string) => Promise<{ token: string }>;
  verifyOtcToken: (token: string) => Promise<void>;
  verifyOtcCode: (token: string, code: string) => Promise<void>;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({
  children,
  onLogin,
  onLogout,
}: {
  children: ReactNode;
  onLogin?: () => void;
  onLogout?: () => void;
}) => {
  const { apiClient, tokenStorage } = useApiClient();

  const [loading, setLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string | null>(null);

  const requestOtc = async (email: string): Promise<{ token: string }> => {
    return await apiClient.post<{ token: string }>("/auth/request_otc", {
      email,
    });
  };

  const verifyOtcToken = async (token: string): Promise<void> => {
    await apiClient.post<void>("/auth/verify_otc_token", {
      token,
    });
  };

  const verifyOtcCode = async (token: string, code: string): Promise<void> => {
    const response = await apiClient.post<{
      refresh_token: string;
      access_token: string;
    }>("/auth/verify_otc_code", { token, code });
    const { access_token: accessToken, refresh_token: refreshToken } = response;
    tokenStorage.saveTokens(accessToken, refreshToken);
    setUserId(getUserId(accessToken));
    onLogin?.();
  };

  const logout = useCallback(async (): Promise<void> => {
    try {
      await apiClient.post("/auth/logout", {
        refresh_token: tokenStorage.getRefreshToken(),
      });
    } catch (error) {
      // do not block the client side logout because of a server error
      console.error("Error during logout:", error);
    }
    tokenStorage.clearTokens();
    setUserId(null);
    setLoading(false);
    onLogout?.();
  }, [onLogout, apiClient, tokenStorage]);

  const getUserId = (accessToken: string): string | null => {
    const parts = accessToken.split(".");
    if (parts.length !== 3 || !parts[1]) {
      return null;
    }
    const payload = JSON.parse(atob(parts[1]));
    return payload.sub;
  };

  const isAccessTokenExpired = (accessToken: string): boolean => {
    const parts = accessToken.split(".");
    if (parts.length !== 3 || !parts[1]) {
      return true;
    }
    const payload = JSON.parse(atob(parts[1]));
    const exp = payload.exp;
    const now = Math.floor(Date.now() / 1000);
    return exp < now;
  };

  useEffect(() => {
    // Check if the user is authenticated on initial load
    // and refresh the access token if necessary
    const checkAuth = async (): Promise<void> => {
      try {
        let accessToken = tokenStorage.getAccessToken();
        // Check if the access token is expired and if so, try to refresh it
        if (accessToken && isAccessTokenExpired(accessToken)) {
          await apiClient.refreshAccessToken();
          // After refreshing, get the new access token
          accessToken = tokenStorage.getAccessToken();
        }
        setUserId(accessToken ? getUserId(accessToken) : null);
        setLoading(false);
      } catch (error) {
        console.error("Error checking authentication:", error);
        await logout();
      }
    };

    void checkAuth();
  }, [logout, apiClient, tokenStorage]);

  return (
    <AuthContext.Provider
      value={{
        userId,
        authenticated: !!userId,
        loading,
        requestOtc,
        verifyOtcToken,
        verifyOtcCode,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
};
