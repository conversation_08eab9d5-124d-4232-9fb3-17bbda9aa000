repos:
  - repo: local
    hooks:
      - id: lint
        name: lint
        entry: pnpm lint
        language: system
        pass_filenames: false
        types: [file]
        stages: [pre-commit, pre-push]
      - id: format
        name: format
        entry: pnpm format
        language: system
        pass_filenames: false
        types: [file]
        stages: [pre-commit, pre-push]
      - id: typescript-check
        name: typescript-check
        entry: pnpm -r exec tsc --noEmit
        language: system
        pass_filenames: false
        types_or: [ts, tsx]
        stages: [pre-push]
