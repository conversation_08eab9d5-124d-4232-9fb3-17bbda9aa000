import path from "path";

import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  outputFileTracingRoot: path.join(__dirname, "../../"),
  transpilePackages: [
    /** Enables hot reloading for local packages without a build step **/
    "@pearl/api-client",
    "@pearl/api-hooks",
    "@pearl/locales",
    /** Tanstack Query compatibility with Safari <14.1 **/
    "@tanstack/react-query",
    "@tanstack/query-core",
  ],
};

export default nextConfig;
