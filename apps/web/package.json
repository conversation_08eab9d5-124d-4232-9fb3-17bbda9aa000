{"name": "@pearl/web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "start": "next start", "lint": "eslint . --max-warnings 190 --cache --cache-location .cache/.eslintcache", "lint:fix": "eslint . --fix --cache --cache-location .cache/.eslintcache", "format": "prettier . --check --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore --ignore-path ./.gitignore --ignore-path ../../.prettierignore", "format:fix": "prettier . --write --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore --ignore-path ./.gitignore --ignore-path ../../.prettierignore", "typecheck": "tsc --noEmit", "heroku-postbuild": "cd ../.. && ./release-tasks.sh"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@pearl/api-client": "workspace:*", "@pearl/api-hooks": "workspace:*", "@pearl/locales": "workspace:*", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.4", "@tailwindcss/postcss": "^4.1.4", "@tanstack/react-query": "^5.75.2", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.493.0", "next": "^15.3.3", "next-themes": "^0.4.6", "postcss": "^8.5.3", "react": "catalog:react19", "react-dom": "catalog:react19", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remove-markdown": "^0.6.2", "sonner": "^2.0.6", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "use-stick-to-bottom": "^1.1.1", "uuid": "^11.1.0"}, "devDependencies": {"@pearl/eslint-config": "workspace:*", "@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "@types/google.accounts": "^0.0.17", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "tailwindcss": "^4.1.4", "typescript": "catalog:"}, "prettier": "@pearl/prettier-config"}