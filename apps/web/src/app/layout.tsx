import "~/app/globals.css";

import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";

import Providers from "~/components/Providers";
import { Toaster } from "~/components/ui/Sonner";

export const metadata: Metadata = {
  title: "Pearl",
};

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased ${inter.className}`}>
        <Providers>{children}</Providers>
        <Toaster />
      </body>
    </html>
  );
}
