"use client";

import <PERSON>ript from "next/script";
import { useEffect } from "react";

import { useSyncUserAccounts } from "@pearl/api-hooks";

import { Separator } from "~/components/ui/Separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/Sidebar";
import CRMRequiredRoute from "~/features/auth/guards/CRMRequiredRoute";
import Sidebar from "~/features/sidebar/components/AppSidebar";

function SyncUserAccounts({ children }: { children: React.ReactNode }) {
  const { mutate } = useSyncUserAccounts();

  // Trigger synchronization only once when the component mounts
  useEffect(() => {
    mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{children}</>;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <CRMRequiredRoute>
      <Script src="https://accounts.google.com/gsi/client" async />
      <SyncUserAccounts>
        <SidebarProvider>
          <Sidebar />
          <SidebarInset>
            <Header />
            {children}
          </SidebarInset>
        </SidebarProvider>
      </SyncUserAccounts>
    </CRMRequiredRoute>
  );
}

function Header() {
  return (
    <header className="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-2 rounded-t-xl border-b bg-background">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator
          orientation="vertical"
          className="mr-2 data-[orientation=vertical]:h-4"
        />
      </div>
    </header>
  );
}
