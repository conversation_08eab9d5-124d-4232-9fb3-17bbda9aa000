"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { useGetUserAccounts } from "@pearl/api-hooks";

export default function Account() {
  const router = useRouter();
  const { data: accounts } = useGetUserAccounts();

  useEffect(() => {
    if (accounts?.[0]) {
      router.push(`/account/${accounts[0].crmId}`);
    }
  }, [accounts, router]);

  return null;
}
