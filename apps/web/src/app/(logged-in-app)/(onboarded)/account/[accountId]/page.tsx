"use client";

import { <PERSON><PERSON><PERSON>, SendH<PERSON>zon<PERSON> } from "lucide-react";
import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import { useChatStream, useGetActions, useGetSummary } from "@pearl/api-hooks";

import {
  AIInput,
  AIInputButton,
  AIInputTextarea,
  AIInputTools,
} from "~/components/ui/kibo/Input";
import {
  AccountActions,
  AccountConversations,
  AccountHeader,
  AccountSummary,
} from "~/features/account/components";

export default function Account() {
  const router = useRouter();
  const { accountId } = useParams<{ accountId: string }>();
  const searchParams = useSearchParams();
  const [localThreadId, setLocalThreadId] = useState<string>(() => uuidv4());
  const [activeTab, setActiveTab] = useState("overview");
  const { data: actionData } = useGetActions();
  const { data: summary } = useGetSummary(accountId);

  useEffect(() => {
    setLocalThreadId(uuidv4());
  }, [setLocalThreadId]);

  const {
    handleSubmit,
    input,
    setInput,
    status: chatStatus,
  } = useChatStream({
    threadId: localThreadId,
    crmAccountId: accountId,
    onError: (error) => {
      console.error("Chat error:", error);
    },
  });

  useEffect(() => {
    const tab = searchParams.get("tab") || "overview";
    setActiveTab(tab);
  }, [searchParams]);

  function handleActionClick(prompt: string) {
    const newThreadId = uuidv4();
    router.push(
      `/account/${accountId}/${newThreadId}?prompt=${encodeURIComponent(prompt)}`,
    );
  }

  const handleSubmitForm = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || chatStatus === "submitted") return;

    handleSubmit(undefined, {});
    handleNewThread(localThreadId);
  };

  const handleNewThread = (threadId: string) => {
    router.push(`/account/${accountId}/${threadId}`);
  };

  const renderContent = () => {
    switch (activeTab) {
      case "overview":
        return renderOverviewContent();
      default:
        return (
          <div className="mt-16">
            <AccountConversations accountId={accountId} />
          </div>
        );
    }
  };
  const renderOverviewContent = () => (
    <div className="mt-6">
      {actionData && actionData.length > 0 && (
        <AccountActions
          actions={actionData}
          onActionClick={handleActionClick}
        />
      )}
      <AccountSummary summary={summary?.summary || ""} />
      <AccountConversations accountId={accountId} />

      <div className="mt-16">
        <AIInput onSubmit={handleSubmitForm}>
          <AIInputTextarea
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask about this account..."
            disabled={chatStatus === "submitted"}
            className="resize-none"
          />
          <div className="mt-2 flex w-full items-center justify-end">
            <AIInputTools>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <Paperclip className="h-4 w-4" />
              </AIInputButton>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <SendHorizontal className="h-4 w-4" />
              </AIInputButton>
            </AIInputTools>
          </div>
        </AIInput>
      </div>
    </div>
  );

  return (
    <div className="mt-8 px-4 sm:px-6 lg:px-8">
      <AccountHeader
        name={"Cegid"}
        employees={"10k"}
        annualRevenue={"$51B"}
        existingRevenue={"$850,000"}
      />

      <div className="mt-6 pb-12">{renderContent()}</div>
    </div>
  );
}
