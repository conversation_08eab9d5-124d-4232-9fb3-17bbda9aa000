import "~/app/globals.css";

import MainContentWrapper from "~/components/MainContentWrapper";
import RedirectIfCRMSetRoute from "~/features/auth/guards/RedirectIfCrmSetRoute";

export default function OnboardingLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <RedirectIfCRMSetRoute>
      <MainContentWrapper>{children}</MainContentWrapper>
    </RedirectIfCRMSetRoute>
  );
}
