"use client";

import { notFound } from "next/navigation";
import { use } from "react";

import { CRMConnectionStep } from "~/features/onboarding/components/CRMConnectionStep";
import { isSupportedCRM } from "~/lib/crm/config";

interface ConnectCRMPageProps {
  params: Promise<{
    crmId: string;
  }>;
}

export default function ConnectCRMPage({ params }: ConnectCRMPageProps) {
  const { crmId } = use(params);

  if (!isSupportedCRM(crmId)) {
    notFound();
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      <CRMConnectionStep crmId={crmId} />
    </div>
  );
}
