"use client";

import { Search } from "lucide-react";
import { useMemo, useState } from "react";

import type { MemberProfile } from "@pearl/api-hooks";
import { useGetMemberProfiles } from "@pearl/api-hooks";

import { Input } from "~/components/ui/Input";
import { cn } from "~/lib/tailwind/cn";

export default function MemberList() {
  const { data: memberProfiles, isLoading: isLoadingMembers } =
    useGetMemberProfiles();
  const [searchQuery, setSearchQuery] = useState("");

  const filteredMembers = useMemo(() => {
    if (!memberProfiles?.members || !searchQuery.trim()) {
      return memberProfiles?.members || [];
    }
    const query = searchQuery.toLowerCase();
    return memberProfiles.members.filter((member) => {
      const fullName = `${member.firstName} ${member.lastName}`.toLowerCase();
      const email = member.email.toLowerCase();

      return fullName.includes(query) || email.includes(query);
    });
  }, [memberProfiles?.members, searchQuery]);

  return (
    <div className="p-6">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-semibold">Members</h1>
            {isLoadingMembers ? null : (
              <div className="rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                {memberProfiles?.members.length}
              </div>
            )}
          </div>
        </div>
        <div className="mt-4 flex items-center gap-4 sm:mt-0 sm:ml-16">
          <div className="relative w-full sm:w-auto">
            <Search className="absolute top-2.5 left-2.5 size-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search members..."
              className="w-full pl-8 sm:w-[200px] md:w-[240px] dark:bg-muted/100"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div className="mt-8 flow-root">
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full align-middle">
            <table className="w-full divide-y divide-border">
              <Header />
              <tbody className="divide-y divide-border">
                {filteredMembers.map((member) => (
                  <MemberRow key={member.id} member={member} />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function Header() {
  return (
    <thead>
      <tr>
        <HeaderCell className="pr-3 pl-4 sm:pl-0">Name</HeaderCell>
        <HeaderCell>Email</HeaderCell>
        <HeaderCell>Role</HeaderCell>
      </tr>
    </thead>
  );
}

function HeaderCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return (
    <th
      scope="col"
      className={cn(
        "px-3 py-3.5 text-left text-sm font-semibold text-foreground",
        className,
      )}
    >
      {children}
    </th>
  );
}

function MemberRow({
  member,
}: Readonly<{
  member: MemberProfile;
}>) {
  return (
    <tr>
      <MemberCell className="pr-3 pl-4 font-medium text-foreground sm:pl-0">
        {member.firstName} {member.lastName}
      </MemberCell>
      <MemberCell>{member.email}</MemberCell>
      <MemberCell>{member.isAdmin ? "Admin" : "Member"}</MemberCell>
    </tr>
  );
}

function MemberCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return (
    <td
      className={cn(
        "px-3 py-4 text-sm whitespace-nowrap text-muted-foreground",
        className,
      )}
    >
      {children}
    </td>
  );
}
