import Image from "next/image";

import { Card, CardContent } from "~/components/ui/Card";

interface ActionCardProps {
  title: string;
  subtitle: string;
  imageSrc: string;
  imageAlt: string;
  imageWidth: number;
  imageHeight: number;
  imageClassName?: string;
  imageStyle?: React.CSSProperties;
  className?: string;
  onClick?: () => void;
}

export function ActionCard({
  title,
  subtitle,
  imageSrc,
  imageAlt,
  imageWidth,
  imageHeight,
  imageClassName,
  imageStyle,
  className,
  onClick,
}: ActionCardProps) {
  return (
    <Card
      className={`group h-[120px] max-w-full min-w-[220px] flex-1 basis-full cursor-pointer overflow-hidden rounded-[12px] transition hover:ring-2 hover:ring-primary/40 sm:basis-[calc(33.333%-1rem)] ${className}`}
      onClick={onClick}
    >
      <CardContent className="relative flex h-full flex-col">
        <div className="flex flex-col">
          <div className="text-[14px] font-semibold">{title}</div>
          <div className="text-[14px] text-muted-foreground">{subtitle}</div>
        </div>
        <Image
          src={imageSrc}
          alt={imageAlt}
          width={imageWidth}
          height={imageHeight}
          className={`mt-auto self-end opacity-80 invert filter transition-transform duration-200 group-hover:-translate-x-1 group-hover:-translate-y-1 dark:filter-none ${imageClassName}`}
          style={imageStyle}
        />
      </CardContent>
    </Card>
  );
}
