import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";

export type OAuthFlowType = "redirect" | "popup";

export interface IntegrationStrategy<Extra = unknown> {
  id: string;
  displayName?: string;
  oauthFlowType?: OAuthFlowType;
  onboarding?: boolean;

  useExtraData?: () => {
    data: Extra | undefined;
    isLoading: boolean;
  };

  connect: (params: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) => Promise<void>;

  disconnect?: (params: {
    apiClient: ApiClient;
    integration: Integration;
  }) => Promise<void>;
}
