import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useGetGoogleCalendarAuthUrl } from "@pearl/api-hooks";

import type { IntegrationStrategy } from "~/features/integrations/types";

interface Extra {
  authUrl: string;
}

export const googleCalendarStrategy: IntegrationStrategy<Extra> = {
  id: "google-calendar",
  displayName: "Google Calendar",
  oauthFlowType: "popup",
  onboarding: false,

  useExtraData: useGetGoogleCalendarAuthUrl,

  connect: ({
    apiClient,
    extra,
  }: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) =>
    new Promise<void>((resolve, reject) => {
      if (!extra?.authUrl) {
        resolve();
        return;
      }

      const url = new URL(extra.authUrl);
      const params = new URLSearchParams(url.search);
      const clientId = params.get("client_id");
      const scope = params.get("scope");
      const state = params.get("state");

      if (!clientId || !scope || !state) {
        reject(new Error("Missing required parameters"));
        return;
      }

      const codeClient = google.accounts.oauth2.initCodeClient({
        client_id: clientId,
        scope,
        state,
        ux_mode: "popup",
        callback: ({ code, state }: { code: string; state: string }) => {
          try {
            void apiClient.get(
              `/workspace/google/calendar/callback?code=${encodeURIComponent(
                code,
              )}&state=${encodeURIComponent(state)}`,
            );
            resolve();
          } catch (err) {
            reject(err);
          }
        },
      });

      codeClient.requestCode();
    }),

  disconnect: ({ apiClient }: { apiClient: ApiClient }) =>
    apiClient.delete(`/workspace/google/calendar/connection`),
};
