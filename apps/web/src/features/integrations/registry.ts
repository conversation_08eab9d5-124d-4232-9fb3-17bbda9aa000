import { defaultStrategy } from "~/features/integrations/strategies/default.strategy";
import { googleCalendarStrategy } from "~/features/integrations/strategies/googleCalendar.strategy";
import { hubspotStrategy } from "~/features/integrations/strategies/hubspot.strategy";
import { salesforceStrategy } from "~/features/integrations/strategies/salesforce.strategy";
import type { IntegrationStrategy } from "~/features/integrations/types";

import { gmailStrategy } from "./strategies/gmail.strategy";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const registry: Record<string, IntegrationStrategy<any>> = {
  [googleCalendarStrategy.id]: googleCalendarStrategy,
  [gmailStrategy.id]: gmailStrategy,
  [hubspotStrategy.id]: hubspotStrategy,
  [salesforceStrategy.id]: salesforceStrategy,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getStrategy(id: string): IntegrationStrategy<any> {
  return registry[id] ?? defaultStrategy;
}
