import type { Action } from "@pearl/api-hooks";

import { ActionCard } from "~/features/home/<USER>/ActionCard";

interface AccountActionsProps {
  actions: Action[];
  onActionClick?: (prompt: string) => void;
}

export default function AccountActions({
  actions,
  onActionClick,
}: AccountActionsProps) {
  const imageConfigs = [
    {
      imageSrc: "/prep-meeting.svg",
      imageAlt: "Prep meeting",
      imageWidth: 67,
      imageHeight: 67,
      imageClassName: "h-12 mr-[-24px]",
      imageStyle: { transform: "rotate(-15deg)" },
    },
    {
      imageSrc: "/draft.svg",
      imageAlt: "Draft email",
      imageWidth: 75,
      imageHeight: 75,
      imageClassName: "h-20 mt-[-10px] mr-[-20px] opacity-90",
      imageStyle: { transform: "rotate(15deg)" },
    },
    {
      imageSrc: "/crm.svg",
      imageAlt: "Update Salesforce",
      imageWidth: 75,
      imageHeight: 75,
      imageClassName: "h-16 mt-[6px] mr-[-36px] opacity-70",
      imageStyle: { transform: "rotate(-15deg)" },
    },
  ] as const;

  // Filter out actions with empty titles and map to card format, taking only the first 3
  const actionCards = actions
    .filter((action) => action.title && action.title.trim() !== "")
    .slice(0, 3)
    .map((action, index) => {
      const imageConfig = imageConfigs[index] ?? imageConfigs[0];
      return {
        title: action.title,
        subtitle: action.description,
        prompt: action.prompt,
        imageSrc: imageConfig.imageSrc,
        imageAlt: imageConfig.imageAlt,
        imageWidth: imageConfig.imageWidth,
        imageHeight: imageConfig.imageHeight,
        imageClassName: imageConfig.imageClassName,
        imageStyle: imageConfig.imageStyle,
      };
    });

  return (
    <div className="mb-8">
      <div className="flex flex-wrap gap-4 px-0 pb-4">
        {actionCards.map((card, index) => (
          <ActionCard
            key={index}
            title={card.title}
            subtitle={card.subtitle}
            imageSrc={card.imageSrc}
            imageAlt={card.imageAlt}
            imageWidth={card.imageWidth}
            imageHeight={card.imageHeight}
            imageClassName={card.imageClassName}
            imageStyle={card.imageStyle}
            onClick={() => {
              onActionClick?.(card.prompt);
            }}
          />
        ))}
      </div>
    </div>
  );
}
