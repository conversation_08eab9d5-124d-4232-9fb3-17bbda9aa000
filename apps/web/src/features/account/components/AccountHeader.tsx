import { BadgeDollarSign, Users } from "lucide-react";

import PageHeader from "~/components/PageHeader";
import { Button } from "~/components/ui/Button";

interface AccountHeaderProps {
  name: string;
  logoUrl?: string;
  employees?: string;
  annualRevenue?: string;
  existingRevenue?: string;
}

export default function AccountHeader({
  name,
  logoUrl = "/placeholder.jpg",
  employees,
  annualRevenue,
  existingRevenue,
}: AccountHeaderProps) {
  const descriptionItems = [
    ...(employees
      ? [
          {
            icon: <Users className="size-4" />,
            label: employees,
          },
        ]
      : []),
    ...(annualRevenue
      ? [
          {
            icon: <BadgeDollarSign className="size-4" />,
            label: annualRevenue,
          },
        ]
      : []),
    ...(existingRevenue
      ? [
          {
            icon: <span className="mx-2 text-muted-foreground/50">|</span>,
            label: <span>Existing revenue: {existingRevenue}</span>,
          },
        ]
      : []),
  ];

  return (
    <PageHeader
      title={name}
      logoUrl={logoUrl}
      descriptionItems={descriptionItems}
      action={<Button variant="secondary">View in Salesforce</Button>}
      variant="enriched"
    />
  );
}
