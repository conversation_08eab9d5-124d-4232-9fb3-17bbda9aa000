import ReactMarkdown from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";

interface AccountSummaryProps {
  summary: string;
  className?: string;
}

export default function AccountSummary({
  summary,
  className = "",
}: AccountSummaryProps) {
  return (
    <div className={`mb-8 ${className}`}>
      <h2 className="mb-3 text-[14px] font-normal text-muted-foreground">
        Summary
      </h2>
      <div className="rounded-lg bg-gradient-to-r from-muted/100 to-transparent p-4">
        <div className="prose-sm prose-p:mt-2 first:prose-p:mt-0 max-w-3xl space-y-3 text-sm leading-relaxed text-foreground">
          <ReactMarkdown remarkPlugins={[remarkGfm, remarkBreaks]}>
            {summary}
          </ReactMarkdown>
        </div>
      </div>
    </div>
  );
}
