import { useRouter } from "next/navigation";
import { useState } from "react";

import Loader from "~/components/Loader";
import { Button } from "~/components/ui/Button";
import { useAccountThreadsWithPreviews } from "~/features/account/hooks/useAccountThreadsWithPreviews";

interface AccountConversationsProps {
  accountId: string;
}

export default function AccountConversations({
  accountId,
}: AccountConversationsProps) {
  const [visibleCount, setVisibleCount] = useState(3);
  const router = useRouter();

  const {
    threads: threadsWithPreviews,
    isLoading,
    isError,
  } = useAccountThreadsWithPreviews(accountId);

  const threadsToShow = threadsWithPreviews.slice(0, visibleCount);
  const hasMoreConversations = threadsWithPreviews.length > visibleCount;

  if (isLoading) {
    return (
      <div>
        <h2 className="mb-2 text-[14px] font-normal text-muted-foreground">
          Conversations
        </h2>
        <Loader />
      </div>
    );
  }

  if (isError) {
    return (
      <div>
        <h2 className="mb-2 text-[14px] font-normal text-muted-foreground">
          Conversations
        </h2>
        <p className="text-sm text-muted-foreground">
          Failed to load conversations
        </p>
      </div>
    );
  }

  return (
    <div>
      <h2 className="mb-2 text-[14px] font-normal text-muted-foreground">
        Conversations
      </h2>

      {threadsToShow.length === 0 ? (
        <p className="text-sm text-muted-foreground">
          No conversations found for this account
        </p>
      ) : (
        <>
          <div className="space-y-3">
            {threadsToShow.map((thread) => (
              <div
                key={thread.id}
                role="button"
                tabIndex={0}
                onClick={() =>
                  router.push(`/account/${accountId}/${thread.id}`)
                }
                className="block cursor-pointer rounded-lg border p-4 transition-colors hover:bg-muted/50"
              >
                <div className="mb-2 flex items-center justify-between">
                  <h3 className="line-clamp-2 text-sm font-medium">
                    {thread.name || thread.createdAt.slice(2, 19)}
                  </h3>
                  <span className="ml-2 shrink-0 text-xs text-muted-foreground">
                    {new Date(thread.updatedAt).toLocaleDateString("en-US", {
                      day: "numeric",
                      month: "short",
                    })}
                  </span>
                </div>

                {thread.messagePreview && (
                  <div className="mt-2">
                    <p className="line-clamp-2 text-xs text-muted-foreground">
                      {thread.messagePreview}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
          {(hasMoreConversations || visibleCount > 3) && (
            <div className="mt-6">
              <Button
                variant="outline"
                className="w-full"
                onClick={() =>
                  hasMoreConversations
                    ? setVisibleCount(
                        Math.min(visibleCount + 5, threadsWithPreviews.length),
                      )
                    : setVisibleCount(3)
                }
              >
                {hasMoreConversations ? "View more" : "Show less"}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
