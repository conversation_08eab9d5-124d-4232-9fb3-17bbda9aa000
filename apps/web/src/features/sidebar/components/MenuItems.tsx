"use client";
import { Home, Users } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
} from "~/components/ui/Sidebar";
import { useIsAdmin } from "~/features/auth/hooks/useIsAdmin";

export default function MenuItems() {
  const pathname = usePathname();
  const router = useRouter();

  const { isAdmin } = useIsAdmin();

  const goToHomePage = () => {
    router.push("/home");
  };

  const goToMemberPage = () => {
    router.push("/members");
  };

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarMenu>
        <SidebarMenuButton
          asChild
          onClick={goToHomePage}
          isActive={pathname.startsWith("/home")}
        >
          <span>
            <Home />
            Home
          </span>
        </SidebarMenuButton>
        {isAdmin ? (
          <SidebarMenuButton
            asChild
            onClick={goToMemberPage}
            isActive={pathname.startsWith("/members")}
          >
            <span>
              <Users />
              Members
            </span>
          </SidebarMenuButton>
        ) : null}
      </SidebarMenu>
    </SidebarGroup>
  );
}
