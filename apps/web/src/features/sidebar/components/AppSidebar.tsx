import Image from "next/image";
import { useRouter } from "next/navigation";

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "~/components/ui/Sidebar";
import AccountList from "~/features/sidebar/components/AccountList";
import ConversationList from "~/features/sidebar/components/ConversationList";
import MenuItems from "~/features/sidebar/components/MenuItems";
import UserMenu from "~/features/sidebar/components/UserMenu";

export default function AppSidebar() {
  const router = useRouter();
  return (
    <Sidebar variant="inset">
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              asChild
              onClick={() => router.push("/")}
            >
              <div>
                <div className="flex aspect-square size-7 items-center justify-center">
                  <Image
                    src="/pearl-logo.svg"
                    alt="Pearl Logo"
                    width={28}
                    height={28}
                    className="size-7"
                    style={{ filter: "var(--logo-filter)" }}
                  />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">Pearl</span>
                  <span className="truncate text-xs opacity-60">Alpha</span>
                </div>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <MenuItems />
        <AccountList />
        <ConversationList />
      </SidebarContent>
      <SidebarFooter>
        <UserMenu />
      </SidebarFooter>
    </Sidebar>
  );
}
