"use client";

import { useGetUserAccounts } from "@pearl/api-hooks";

import Loader from "~/components/Loader";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
} from "~/components/ui/Sidebar";
import AccountWithThreadsItem from "~/features/sidebar/components/AccountWithThreadsItem";

export default function AccountList() {
  const {
    data: accounts,
    isError: isErrorAccounts,
    isLoading: isLoadingAccounts,
  } = useGetUserAccounts();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Accounts</SidebarGroupLabel>
      {isLoadingAccounts ? (
        <Loader />
      ) : (
        <SidebarMenu>
          {accounts?.map((account) => (
            <AccountWithThreadsItem key={account.crmId} account={account} />
          ))}
        </SidebarMenu>
      )}
      {!isLoadingAccounts && isErrorAccounts ? (
        <div>Error loading accounts</div>
      ) : null}
    </SidebarGroup>
  );
}
