"use client";

import { ChevronRight, Folder } from "lucide-react";
import { usePara<PERSON>, useRouter } from "next/navigation";

import type { Account } from "@pearl/api-hooks";
import { useGetAccountThreads } from "@pearl/api-hooks";

import {
  Collapsible as CollapsibleComponent,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/Collapsible";
import {
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "~/components/ui/Sidebar";

export default function AccountWithThreadsItem({
  account,
}: {
  account: Account;
}) {
  const router = useRouter();
  const { accountId: accountIdFromParams, threadId: threadIdFromParams } =
    useParams();

  const { data: accountThreadsResponse } = useGetAccountThreads(
    accountIdFromParams as string | undefined,
  );

  const goToAccount = (account: Account) => {
    router.push(`/account/${account.crmId}`);
  };

  const goToThread = (threadId: string) => {
    router.push(`/account/${account.crmId}/${threadId}`);
  };

  const isAccountActive = accountIdFromParams === account.crmId;

  const isThreadActive = (threadId: string) => {
    return (
      threadIdFromParams === threadId && accountIdFromParams === account.crmId
    );
  };

  return (
    <CollapsibleComponent asChild>
      <SidebarMenuItem>
        <SidebarMenuButton
          asChild
          isActive={isAccountActive}
          onClick={() => goToAccount(account)}
        >
          <span>
            <Folder /> {account.crmName}
          </span>
        </SidebarMenuButton>
        <CollapsibleTrigger asChild>
          <SidebarMenuAction className="data-[state=open]:rotate-90">
            <ChevronRight />
            <span className="sr-only">Toggle</span>
          </SidebarMenuAction>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarMenuSub>
            {accountThreadsResponse
              ? accountThreadsResponse.threads
                  .sort((a, b) => a.createdAt.localeCompare(b.createdAt))
                  .map((thread) => (
                    <SidebarMenuSubItem
                      key={thread.id}
                      onClick={() => goToThread(thread.id)}
                    >
                      <SidebarMenuSubButton
                        isActive={isThreadActive(thread.id)}
                      >
                        {thread.name || thread.createdAt.slice(2, 19)}
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))
              : null}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </CollapsibleComponent>
  );
}
