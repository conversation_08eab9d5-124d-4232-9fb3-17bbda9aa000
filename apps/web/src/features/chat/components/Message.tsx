import type { UIMessage } from "ai";

import Markdown from "~/components/Markdown";
import { cn } from "~/lib/tailwind/cn";

interface MessageProps {
  content: string;
  role: UIMessage["role"];
}

const chatBubbleClasses = cn(
  // --- Base styles for ALL bubbles ---
  "max-w-[80%]",
  "flex-col gap-2 rounded-3xl px-4 py-3 text-sm",

  // --- User-specific styles (Light & Dark) ---
  "group-[.is-user]:ml-12",
  "group-[.is-user]:bg-[var(--user-message-background)]",
  "group-[.is-user]:text-[var(--user-message-foreground)]",
  "dark:group-[.is-user]:bg-[#4F5257]",
  "dark:group-[.is-user]:text-white",

  // --- Assistant-specific styles (Light & Dark) ---
  "group-[.is-assistant]:bg-gradient-to-r",
  "group-[.is-assistant]:from-[#EEF0FC] group-[.is-assistant]:to-[rgb(246,247,253)]",
  "dark:group-[.is-assistant]:from-[#303236] dark:group-[.is-assistant]:to-[#242629]",
);

export default function Message({ content, role }: MessageProps) {
  const isUser = role === "user";

  return (
    <div
      className={cn(
        "w- group flex items-end gap-2 py-4",
        isUser ? "is-user justify-end" : "is-assistant justify-start",
      )}
    >
      <div className={chatBubbleClasses}>
        <div className="break-words whitespace-pre-wrap">
          <Markdown content={content} />
        </div>
      </div>
    </div>
  );
}
