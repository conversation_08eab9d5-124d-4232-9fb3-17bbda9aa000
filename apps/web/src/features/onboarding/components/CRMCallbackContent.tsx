import { ErrorStep } from "~/features/onboarding/components/ErrorStep";
import { LoadingStep } from "~/features/onboarding/components/LoadingStep";
import { SuccessStep } from "~/features/onboarding/components/SuccessStep";
import { getCRMConfig } from "~/lib/crm/config";

interface CRMCallbackContentProps {
  crmId: string;
  isLoading: boolean;
  isImporting: boolean;
  isSuccess: boolean;
  isError: boolean;
  errorMessage: string | null;
}

export const CRMCallbackContent = ({
  crmId,
  isLoading,
  isImporting,
  isSuccess,
  isError,
  errorMessage,
}: CRMCallbackContentProps) => {
  const config = getCRMConfig(crmId);

  if (!config) {
    return null;
  }

  let content = null;

  if (isLoading) {
    content = (
      <LoadingStep
        integrationId={config.id}
        title={config.connectingTitle}
        stage="connecting"
      />
    );
  } else if (isImporting) {
    content = (
      <LoadingStep
        integrationId={config.id}
        title={config.importingTitle}
        stage="importing"
      />
    );
  } else if (isSuccess) {
    content = <SuccessStep />;
  } else if (isError) {
    content = <ErrorStep errorMessage={errorMessage} />;
  }

  if (!content) {
    return null;
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      {content}
    </div>
  );
};
