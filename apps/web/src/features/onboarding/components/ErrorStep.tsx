interface ErrorStepProps {
  errorMessage: string | null;
}

export const ErrorStep = ({ errorMessage }: ErrorStepProps) => {
  return (
    <div className="text-center">
      <h2 className="mb-4 text-xl font-bold text-red-500">Connection Error</h2>
      <p className="mb-6 text-gray-600">
        {errorMessage || "An error occurred while connecting to your CRM"}
      </p>
      <p className="text-gray-500">Redirecting back to try again...</p>
    </div>
  );
};
