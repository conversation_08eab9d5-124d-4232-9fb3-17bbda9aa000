import { useRouter } from "next/navigation";

import { AuthProvider as BaseAuthProvider } from "@pearl/api-client";
import { queryClient } from "@pearl/api-hooks/query-client";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const handleLogin = () => {
    router.push("/");
  };
  const handleLogout = () => {
    queryClient.clear();
    router.push("/login/email");
  };
  return (
    <BaseAuthProvider onLogin={handleLogin} onLogout={handleLogout}>
      {children}
    </BaseAuthProvider>
  );
};
