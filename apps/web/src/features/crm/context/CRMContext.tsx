import type { QueryObserverResult } from "@tanstack/react-query";
import type { ReactNode } from "react";
import { createContext, useContext } from "react";

import type { UserCRM } from "@pearl/api-hooks";
import { useGetUserCRM } from "@pearl/api-hooks";

interface CRMContextType {
  userCRM: UserCRM | null;
  isLoading: boolean;
  isError: boolean;
  refreshUserCRM: () => Promise<QueryObserverResult<UserCRM | null, Error>>;
}

const CRMContext = createContext<CRMContextType | undefined>(undefined);

export function CRMProvider({ children }: { children: ReactNode }) {
  const { data, isLoading, isError, refetch } = useGetUserCRM();

  return (
    <CRMContext.Provider
      value={{
        userCRM: data ?? null,
        isLoading,
        isError,
        refreshUserCRM: refetch,
      }}
    >
      {children}
    </CRMContext.Provider>
  );
}

export function useCRM() {
  const context = useContext(CRMContext);
  if (context === undefined) {
    throw new Error("useCRM must be used within a CRMProvider");
  }
  return context;
}
