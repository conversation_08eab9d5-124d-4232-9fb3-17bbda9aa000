import { cn } from "~/lib/tailwind/cn";

type SourceIconProps = {
  width?: number;
  height?: number;
} & Omit<React.SVGProps<SVGSVGElement>, "width" | "height">;

export default function SourceIcon({
  width = 80,
  height = 80,
  className = "",
  ...props
}: SourceIconProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 80 80"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
      style={{
        filter:
          "drop-shadow(0px 6px 14px rgba(0, 0, 0, 0.1)) drop-shadow(0px 25px 25px rgba(0, 0, 0, 0.09)) drop-shadow(0px 55px 33px rgba(0, 0, 0, 0.05)) drop-shadow(0px 99px 39px rgba(0, 0, 0, 0.01))",
      }}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M58.6666 0C66.134 0 69.8677 5.61067e-05 72.7198 1.4533C75.2287 2.73161 77.2684 4.77134 78.5467 7.28017C79.9999 10.1323 80 13.866 80 21.3334V37.3333C80 52.2681 80 59.7355 77.0935 65.4398C74.5369 70.4574 70.4574 74.5369 65.4398 77.0935C59.7355 80 52.2681 80 37.3333 80H21.3334C13.866 80 10.1323 79.9999 7.28017 78.5467C4.77134 77.2684 2.73161 75.2287 1.4533 72.7198C5.61067e-05 69.8677 0 66.134 0 58.6666V42.6667C0 27.7319 -4.26021e-05 20.2645 2.90645 14.5602C5.46307 9.54256 9.54256 5.46307 14.5602 2.90645C20.2645 -4.26021e-05 27.7319 0 42.6667 0H58.6666ZM40 20C28.9543 20 20 28.9543 20 40C20 51.0457 28.9543 60 40 60C51.0457 60 60 51.0457 60 40C60 28.9543 51.0457 20 40 20Z"
      />
    </svg>
  );
}
