import { cn } from "~/lib/tailwind/cn";

type SalesforceCloudIconProps = {
  variant?: "solid" | "dashed";
  width?: number;
  height?: number;
} & Omit<React.SVGProps<SVGSVGElement>, "width" | "height">;

export default function SalesforceCloudIcon({
  variant = "solid",
  width = 80,
  height = 80,
  className = "",
  ...props
}: SalesforceCloudIconProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 80 80"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn(className)}
      style={{
        filter:
          "drop-shadow(0px 6px 14px rgba(0, 0, 0, 0.1)) drop-shadow(0px 25px 25px rgba(0, 0, 0, 0.09)) drop-shadow(0px 55px 33px rgba(0, 0, 0, 0.05)) drop-shadow(0px 99px 39px rgba(0, 0, 0, 0.01))",
      }}
      {...props}
    >
      {variant === "solid" ? (
        <>
          <rect width="80" height="80" rx="20" fill="white" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M36.1976 27.5915C37.6596 26.0685 39.6964 25.1228 41.948 25.1228C44.941 25.1228 47.5531 26.7921 48.9437 29.2703C50.1879 28.7145 51.5356 28.428 52.8983 28.4297C58.2971 28.4297 62.6732 32.8445 62.6732 38.2914C62.6732 43.7383 58.2971 48.1531 52.8983 48.1531C52.2382 48.1531 51.5945 48.0871 50.9721 47.9617C49.7476 50.1453 47.4127 51.6212 44.7342 51.6212C43.6128 51.6212 42.552 51.3633 41.6083 50.9016C40.3664 53.8227 37.4736 55.8699 34.1027 55.8699C30.5925 55.8699 27.6 53.6481 26.4519 50.5331C25.9404 50.641 25.419 50.6954 24.8962 50.6953C20.7159 50.6958 17.3272 47.2718 17.3272 43.0479C17.324 41.7093 17.6718 40.3933 18.3358 39.231C18.9999 38.0687 19.9569 37.1008 21.1117 36.4237C20.6326 35.3198 20.3861 34.1291 20.3876 32.9258C20.3876 28.0676 24.3308 24.1299 29.195 24.1299C30.55 24.1283 31.8871 24.4399 33.1018 25.0404C34.3165 25.6409 35.376 26.514 36.1976 27.5915Z"
            fill="#00A1E0"
          />
        </>
      ) : (
        <>
          <rect
            x="0.5"
            y="0.5"
            width="79"
            height="79"
            rx="19.5"
            stroke="#6A7282"
            strokeDasharray="3 3"
          />
          <path
            d="M17.8269 43.0469L17.8298 42.8125C17.8369 42.5791 17.8558 42.3468 17.8855 42.1162L17.3894 42.0527L17.3884 42.0518C17.4735 41.3909 17.6447 40.7436 17.8972 40.127L17.8982 40.1279L18.3611 40.3174C18.4493 40.102 18.5477 39.8902 18.657 39.6836L18.7693 39.4795C18.9241 39.2085 19.0964 38.9482 19.2839 38.7012L18.8855 38.3984C19.2885 37.8677 19.7588 37.3913 20.2849 36.9824L20.2859 36.9834L20.5925 37.3779C20.7762 37.2352 20.9672 37.1013 21.1648 36.9766L21.364 36.8555L21.7468 36.6309L21.5701 36.2246L21.4109 35.8311C21.3614 35.6988 21.3158 35.5651 21.2732 35.4307L20.7966 35.582H20.7957C20.6144 35.0098 20.4922 34.4204 20.4314 33.8232L20.9294 33.7734C20.9152 33.6331 20.9053 33.4917 20.8982 33.3506L20.8875 32.9268V32.9258C20.8875 32.3709 20.9419 31.8292 21.0457 31.3057L20.5554 31.208C20.7843 30.0536 21.2387 28.9801 21.8718 28.0361L22.2878 28.3154C22.8947 27.4107 23.6745 26.631 24.5808 26.0254L24.3035 25.6104C25.2483 24.979 26.3222 24.5259 27.4773 24.2979L27.574 24.7881C27.9669 24.7105 28.3698 24.6598 28.781 24.6396L29.1951 24.6299C29.5139 24.6295 29.832 24.6475 30.1472 24.6836L30.2029 24.1865C30.8742 24.2632 31.5346 24.4167 32.1707 24.6445V24.6455L32.0017 25.1162C32.3004 25.2232 32.5938 25.347 32.8796 25.4883C33.1655 25.6296 33.4423 25.7876 33.7087 25.96L33.9802 25.54C34.5472 25.9067 35.0695 26.3379 35.5378 26.8242H35.5388L35.1785 27.1709C35.3986 27.3995 35.6062 27.6409 35.7996 27.8945L36.1541 28.3594L36.5583 27.9375C36.9071 27.5743 37.2905 27.2463 37.7029 26.959L37.4177 26.5479C38.2882 25.9415 39.2812 25.5053 40.3503 25.2852L40.4519 25.7744C40.9351 25.675 41.4355 25.623 41.948 25.623C42.6311 25.6231 43.2922 25.716 43.9207 25.8896L44.0535 25.4072C45.4451 25.7916 46.6872 26.5481 47.6726 27.5654L47.3142 27.9131C47.7761 28.39 48.1784 28.9281 48.5076 29.5146L48.7302 29.9131L49.1472 29.7266C49.4417 29.595 49.7422 29.4793 50.0476 29.3799L49.8933 28.9043C50.539 28.6941 51.2052 28.5521 51.8806 28.4814L51.8816 28.4824L51.9333 28.9795C52.0933 28.9628 52.2539 28.9497 52.4148 28.9414L52.8972 28.9297H52.8982C53.3108 28.9297 53.7171 28.9572 54.115 29.0098L54.1794 28.5156C55.0448 28.6299 55.8743 28.8574 56.6531 29.1846L56.4607 29.6445C57.2152 29.9616 57.9197 30.3767 58.5583 30.874L58.864 30.4814C59.5398 31.0076 60.145 31.6207 60.6638 32.3047L60.2683 32.6064C60.7565 33.2503 61.1635 33.96 61.4744 34.7197L61.9353 34.5303C62.2549 35.3111 62.4761 36.1422 62.5876 37.0088L62.0955 37.0723C62.1468 37.4708 62.1726 37.8779 62.1726 38.291C62.1726 38.7042 62.1467 39.1112 62.0955 39.5098L62.5876 39.5732C62.4762 40.4396 62.2547 41.2702 61.9353 42.0508L61.4744 41.8633C61.1635 42.623 60.7565 43.3328 60.2683 43.9766L60.6638 44.2773C60.145 44.9614 59.5398 45.5743 58.864 46.1006L58.5583 45.708C57.9197 46.2053 57.2152 46.6204 56.4607 46.9375L56.6531 47.3975C55.8743 47.7247 55.0448 47.9511 54.1794 48.0654L54.115 47.5732C53.7171 47.6258 53.3108 47.6533 52.8982 47.6533C52.7439 47.6533 52.5905 47.6491 52.4382 47.6416L52.4138 48.1406C52.0876 48.1246 51.7658 48.0924 51.449 48.0449L51.5232 47.5508C51.3712 47.528 51.2205 47.5018 51.071 47.4717L50.7136 47.3994L50.5359 47.7168C50.2471 48.2319 49.8915 48.705 49.4812 49.124L49.8376 49.4736C48.9609 50.3688 47.8514 51.0335 46.6072 51.3711L46.4773 50.8896C45.9224 51.0402 45.3378 51.1211 44.7341 51.1211C44.4799 51.1211 44.2286 51.1068 43.9822 51.0791L43.9265 51.5742C43.3811 51.5129 42.8543 51.3913 42.3533 51.2139L42.5203 50.7432C42.2829 50.6591 42.0519 50.5618 41.8279 50.4521L41.3542 50.2207L41.1482 50.7061C41.0016 51.051 40.8301 51.3833 40.6365 51.7002L41.0632 51.96C40.6472 52.641 40.1333 53.255 39.5427 53.7852L39.2097 53.4141C38.6568 53.9105 38.0319 54.3279 37.3523 54.6475L37.5642 55.0986C36.8558 55.4317 36.0919 55.6651 35.2898 55.7822L35.2185 55.2891C34.8546 55.3422 34.4815 55.3701 34.1023 55.3701C33.7073 55.3701 33.3195 55.3399 32.9412 55.2822L32.866 55.7754C32.031 55.6481 31.2381 55.3939 30.5066 55.0332L30.7273 54.5869C30.0261 54.2413 29.3853 53.7904 28.825 53.2559L28.4802 53.6172C27.8827 53.0472 27.3707 52.3881 26.9675 51.6602L27.406 51.418C27.2188 51.0801 27.0558 50.7269 26.9207 50.3604L26.7712 49.9551L26.3484 50.0439C26.2291 50.0691 26.1093 50.0914 25.989 50.1104L26.0662 50.6035C25.8083 50.644 25.5485 50.672 25.2878 50.6855L25.2615 50.1855C25.1399 50.1919 25.0181 50.1953 24.8962 50.1953L24.5417 50.1865C24.1902 50.169 23.8453 50.126 23.5095 50.0586L23.4109 50.5469C22.4147 50.3467 21.4891 49.9497 20.6755 49.3965L20.9568 48.9834C20.1871 48.4601 19.5243 47.7878 19.0095 47.0068L18.5925 47.2812C18.0527 46.4622 17.6642 45.5325 17.4695 44.5322L17.9607 44.4375C17.8951 44.1006 17.8527 43.7549 17.8357 43.4023L17.8269 43.0479V43.0469Z"
            stroke="#6A7282"
            strokeDasharray="3 3"
          />
        </>
      )}
    </svg>
  );
}
