"use client";
import { Loader2Icon, SendIcon, SquareIcon, XIcon } from "lucide-react";
import type {
  ComponentProps,
  HTMLAttributes,
  KeyboardEventHandler,
} from "react";
import {
  Children,
  forwardRef,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";

import { Button } from "~/components/ui/Button";
import { Textarea } from "~/components/ui/Textarea";
import { cn } from "~/lib/tailwind/cn";

interface UseAutoResizeTextareaProps {
  minHeight: number;
  maxHeight?: number;
}
const useAutoResizeTextarea = ({
  minHeight,
  maxHeight,
}: UseAutoResizeTextareaProps) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const adjustHeight = useCallback(
    (reset?: boolean) => {
      const textarea = textareaRef.current;
      if (!textarea) {
        return;
      }
      if (reset) {
        textarea.style.height = `${minHeight}px`;
        return;
      }
      // Temporarily shrink to get the right scrollHeight
      textarea.style.height = `${minHeight}px`;
      // Calculate new height
      const newHeight = Math.max(
        minHeight,
        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY),
      );
      textarea.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight],
  );
  useEffect(() => {
    // Set initial height
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = `${minHeight}px`;
    }
  }, [minHeight]);
  // Adjust height on window resize
  useEffect(() => {
    const handleResize = () => adjustHeight();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [adjustHeight]);
  return { textareaRef, adjustHeight };
};

export type AIInputProps = HTMLAttributes<HTMLFormElement>;
export const AIInput = ({ className, ...props }: AIInputProps) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <div
      className={cn(
        "relative min-h-[120px] p-[1px]",
        isMobile &&
          "fixed bottom-0 left-1/2 z-50 w-screen max-w-none -translate-x-1/2",
      )}
    >
      {/* Upward shadow behind gradient */}
      <div
        className={cn(
          "absolute inset-0 -top-8 h-8 bg-gradient-to-t from-background to-transparent",
          isMobile ? "rounded-t-3xl" : "rounded-3xl",
        )}
      />
      {/* Gradient border background */}
      <div
        className={cn(
          "absolute inset-0 [background-image:linear-gradient(0deg,#FFFFFFFF_90%,#FFFFFF00_100%),linear-gradient(90deg,#B5BDEDFF_1%,#FFC3A8FF_100%)] [background-size:100%_100%] [background-position:0px_0px,0px_0px] dark:[background-image:linear-gradient(0deg,#242629_90%,#24262900_100%),linear-gradient(90deg,#9DA6D7FF_0%,#DD9E7DFF_99%)]",
          isMobile ? "rounded-t-3xl" : "rounded-3xl",
        )}
      />
      {/* Main content */}
      <form
        className={cn(
          "relative flex min-h-[120px] w-full flex-col bg-white pt-5 pr-5 pb-3 pl-5 shadow-[0px_1px_3px_0px_rgba(17,_12,_46,_0.15)] ring-offset-background transition-colors dark:bg-[#2d2f33] dark:shadow-none",
          isMobile
            ? "w-screen max-w-none rounded-t-3xl px-4 text-base"
            : "rounded-3xl",
          className,
        )}
        {...props}
      />
    </div>
  );
};

export type AIInputTextareaProps = ComponentProps<typeof Textarea> & {
  minHeight?: number;
  maxHeight?: number;
};
export const AIInputTextarea = forwardRef<
  HTMLTextAreaElement,
  AIInputTextareaProps
>(
  (
    {
      onChange,
      className,
      placeholder = "What would you like to know?",
      minHeight = 48,
      maxHeight = 164,
      value,
      ...props
    },
    forwardedRef,
  ) => {
    const { textareaRef, adjustHeight } = useAutoResizeTextarea({
      minHeight,
      maxHeight,
    });

    // Adjust height when value changes programmatically
    useEffect(() => {
      if (value !== undefined) {
        requestAnimationFrame(() => {
          adjustHeight();
        });
      }
    }, [value, adjustHeight]);

    const combinedRef = useCallback(
      (node: HTMLTextAreaElement | null) => {
        if (textareaRef.current !== node) {
          (textareaRef as { current: HTMLTextAreaElement | null }).current =
            node;
        }

        if (typeof forwardedRef === "function") {
          forwardedRef(node);
        } else if (forwardedRef) {
          forwardedRef.current = node;
        }
      },
      [textareaRef, forwardedRef],
    );

    const handleKeyDown: KeyboardEventHandler<HTMLTextAreaElement> = (e) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        const form = e.currentTarget.form;
        if (form) {
          form.requestSubmit();
        }
      }
    };
    return (
      <Textarea
        className={cn(
          "w-full resize-none rounded-none border-none p-0 shadow-none ring-0 outline-none",
          "bg-transparent dark:bg-transparent",
          "focus-visible:ring-0",
          className,
        )}
        name="message"
        value={value}
        onChange={(e) => {
          adjustHeight();
          onChange?.(e);
        }}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        ref={combinedRef}
        {...props}
      />
    );
  },
);

AIInputTextarea.displayName = "AIInputTextarea";
export type AIInputToolbarProps = HTMLAttributes<HTMLDivElement>;
export const AIInputToolbar = ({
  className,
  ...props
}: AIInputToolbarProps) => (
  <div
    className={cn("flex items-center justify-between p-1", className)}
    {...props}
  />
);
export type AIInputToolsProps = HTMLAttributes<HTMLDivElement>;
export const AIInputTools = ({ className, ...props }: AIInputToolsProps) => (
  <div
    className={cn(
      "flex items-center gap-1",
      "[&_button:first-child]:rounded-bl-xl",
      className,
    )}
    {...props}
  />
);

export type AIInputButtonProps = ComponentProps<typeof Button>;
export const AIInputButton = ({
  variant = "ghost",
  className,
  size,
  ...props
}: AIInputButtonProps) => {
  const newSize =
    (size ?? Children.count(props.children) > 1) ? "default" : "icon";
  return (
    <Button
      className={cn(
        "shrink-0 gap-1.5 rounded-lg",
        variant === "ghost" && "text-muted-foreground",
        newSize === "default" && "px-3",
        className,
      )}
      size={newSize}
      type="button"
      variant={variant}
      {...props}
    />
  );
};

export type AIInputSubmitProps = ComponentProps<typeof Button> & {
  status?: "submitted" | "streaming" | "ready" | "error";
};
export const AIInputSubmit = ({
  className,
  variant = "default",
  size = "icon",
  status,
  children,
  ...props
}: AIInputSubmitProps) => {
  let Icon = <SendIcon />;
  if (status === "submitted") {
    Icon = <Loader2Icon className="animate-spin" />;
  } else if (status === "streaming") {
    Icon = <SquareIcon />;
  } else if (status === "error") {
    Icon = <XIcon />;
  }
  return (
    <Button
      className={cn("gap-1.5 rounded-lg rounded-br-xl", className)}
      size={size}
      type="submit"
      variant={variant}
      {...props}
    >
      {children ?? Icon}
    </Button>
  );
};
