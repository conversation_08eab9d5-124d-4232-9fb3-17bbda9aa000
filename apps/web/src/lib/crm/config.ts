export type CRMId = "salesforce" | "hubspot";

export const SUPPORTED_CRMS: CRMId[] = ["salesforce", "hubspot"];

export interface CRMConfig {
  id: CRMId;
  name: string;
  displayName: string;
  description: string;
  logoPath: string;
  connectingTitle: string;
  importingTitle: string;
}

export const CRM_CONFIGS: Record<CRMId, CRMConfig> = {
  salesforce: {
    id: "salesforce",
    name: "salesforce",
    displayName: "Salesforce",
    description: "Connect to your Salesforce CRM.",
    logoPath: "/integrations/salesforce.png",
    connectingTitle: "Processing Salesforce connection...",
    importingTitle: "Synchronizing your accounts...",
  },
  hubspot: {
    id: "hubspot",
    name: "hubspot",
    displayName: "HubSpot",
    description: "Connect to your HubSpot CRM.",
    logoPath: "/integrations/hubspot.png",
    connectingTitle: "Processing HubSpot connection...",
    importingTitle: "Synchronizing your accounts...",
  },
};

export function getCRMConfig(crmId: string): CRMConfig | undefined {
  return CRM_CONFIGS[crmId as CRMId];
}

export function isSupportedCRM(crmId: string): crmId is CRMId {
  return SUPPORTED_CRMS.includes(crmId as CRMId);
}

export function getAllCRMConfigs(): CRMConfig[] {
  return Object.values(CRM_CONFIGS);
}
