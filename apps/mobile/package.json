{"name": "pearl", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"// ==================== CLEAN ==================== //": "", "clean": "git clean -xdf .cache .expo .turbo android ios node_modules", "// ==================== START ==================== //": "", "start": "expo start -c", "android": "expo run:android", "ios": "expo run:ios", "// ==================== UPGRADE ==================== //": "", "check:tamagui": "tamagui check", "upgrade:tamagui": "yarn up '*tamagui*'@latest '@tamagui/*'@latest", "// ==================== PRETTIER ==================== //": "", "format": "prettier . --check --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore --ignore-path ./.gitignore", "format:fix": "prettier . --write --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore --ignore-path ./.gitignore", "// ==================== LINT ==================== //": "", "lint": "eslint . --max-warnings 0 --cache --cache-location .cache/.eslintcache", "lint:fix": "eslint . --fix --cache --cache-location .cache/.eslintcache", "lint:generate-output": "eslint . --format json --output-file eslint-output.json", "lint:disable-inserter": "eslint-disable-inserter < eslint-output.json && rm -rf eslint-output.json", "lint:auto-disable": "pnpm lint:generate-output && pnpm lint:disable-inserter", "// ==================== TYPESCRIPT ==================== //": "", "typecheck": "tsc --noEmit", "// ==================== TEST ==================== //": "", "test": "jest --maxWorkers=2", "test:changed": "pnpm test --only<PERSON><PERSON>ed", "// ==================== CI ==================== //": "", "eas-build-pre-install": "echo node-linker=hoisted >> ../../.npmrc", "check:config": "npx expo config", "ci:check": "pnpm typecheck && pnpm lint && pnpm format && pnpm test", "ci:fix": "pnpm format:fix && pnpm lint:fix", "// ==================== BUILD ==================== //": "", "build:ios:simulator": "eas build --profile development-simulator --platform ios", "build:android:simulator": "eas build --profile development-simulator --platform android", "build:ios:device": "eas build --profile development --platform ios", "build:android:device": "eas build --profile development --platform android", "build:ios:preview": "eas build --profile preview --platform ios", "build:android:preview": "eas build --profile preview --platform android", "build:ios:production": "eas build --profile production --platform ios", "build:android:production": "eas build --profile production --platform android"}, "prettier": "@pearl/prettier-config", "jest": {"preset": "jest-expo"}, "dependencies": {"@dev-plugins/react-query": "^0.3.1", "@expo-google-fonts/inter": "^0.3.0", "@expo/config": "^11.0.10", "@pearl/api-client": "workspace:*", "@pearl/api-hooks": "workspace:*", "@pearl/locales": "workspace:*", "@react-navigation/drawer": "^7.3.12", "@react-navigation/elements": "^2.4.2", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@shopify/react-native-skia": "2.0.0-next.4", "@stardazed/streams-text-encoding": "^1.0.2", "@tamagui/config": "^1.126.12", "@tamagui/lucide-icons": "^1.126.12", "@tamagui/toast": "^1.126.12", "@tanstack/react-query": "^5.75.2", "@ungap/structured-clone": "^1.3.0", "ai": "^4.3.16", "babel-preset-expo": "~13.1.11", "burnt": "^0.13.0", "dayjs": "^1.11.13", "expo": "~53.0.9", "expo-audio": "^0.4.5", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-dev-menu": "^6.1.11", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.13", "expo-web-browser": "~14.1.6", "i18next": "^25.1.3", "jotai": "^2.12.4", "react": "catalog:react19", "react-dom": "catalog:react19", "react-i18next": "^15.5.1", "react-native": "0.79.2", "react-native-confirmation-code-field": "^7.6.1", "react-native-gesture-handler": "~2.24.0", "react-native-keyboard-controller": "^1.17.1", "react-native-markdown-display": "^7.0.2", "react-native-mmkv": "^3.2.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-webview": "13.13.5", "tamagui": "^1.126.12"}, "devDependencies": {"@babel/core": "^7.24.6", "@eslint/eslintrc": "^3.3.1", "@expo/metro-config": "~0.20.0", "@expo/metro-runtime": "~5.0.4", "@pearl/eslint-config": "workspace:*", "@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "@tamagui/babel-plugin": "^1.126.12", "@tamagui/cli": "^1.126.12", "@tamagui/metro-plugin": "^1.126.12", "@types/jest": "^29.5.14", "@types/react": "catalog:react19", "@types/ungap__structured-clone": "^1.2.0", "eslint": "catalog:", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-typescript-sort-keys": "^3.3.0", "jest": "~29.7.0", "jest-expo": "~53.0.5", "prettier": "catalog:", "react-native-svg-transformer": "^1.5.1", "typescript": "catalog:"}}