import { defaultConfig } from "@tamagui/config/v4";
import { createTamagui } from "tamagui";

import { fonts } from "@theme/fonts";
import { themes } from "@theme/themes";
import { tokens } from "@theme/tokens";

export const config = createTamagui({
  ...defaultConfig,
  tokens,
  themes,
  fonts,
  settings: {
    ...defaultConfig.settings,
    onlyAllowShorthands: false,
    allowedStyleValues: "strict",
    autocompleteSpecificTokens: "except-special",
  },
});

export default config;

export type Conf = typeof config;

declare module "tamagui" {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  interface TamaguiCustomConfig extends Conf {}
}
