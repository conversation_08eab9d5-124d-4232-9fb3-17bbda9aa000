import type { Key } from "@utils/storage";
import { Storage } from "@utils/storage";

import type { TokenStorageAdapter } from "@pearl/api-client";

class TokenStorage implements TokenStorageAdapter {
  private accessTokenKey: Key = "accessToken";
  private refreshTokenKey: Key = "refreshToken";
  getAccessToken(): string | null {
    return Storage.getString(this.accessTokenKey) ?? null;
  }
  getRefreshToken(): string | null {
    return Storage.getString(this.refreshTokenKey) ?? null;
  }
  saveTokens(accessToken: string, refreshToken: string): void {
    Storage.set(this.accessTokenKey, accessToken);
    Storage.set(this.refreshTokenKey, refreshToken);
  }
  clearTokens(): void {
    Storage.del(this.accessTokenKey);
    Storage.del(this.refreshTokenKey);
  }
}

export const tokenStorage = new TokenStorage();
