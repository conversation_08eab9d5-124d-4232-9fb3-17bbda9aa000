import { defaultConfig } from "@tamagui/config/v4";
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import type { useTheme } from "tamagui";

// Please keep relative imports for theme files, otherwise it will break the build process
import { darkColors, lightColors } from "./colors";

const light = {
  ...defaultConfig.themes.light,
  ...lightColors,
  drawerBackground: "#F0EFF6",
};

const dark = {
  ...defaultConfig.themes.dark,
  ...darkColors,
  drawerBackground: "#1A1B1F",
};

type BaseTheme = typeof dark;

const allThemes = {
  light,
  dark,
};

type ThemeName = keyof typeof allThemes;

type Themes = Record<ThemeName, BaseTheme>;

export const themes: Themes = allThemes;

export type UseThemeResult = ReturnType<typeof useTheme>;
