import { createSystemFont } from "@tamagui/config/v4";

export const fonts = {
  body: createSystemFont({
    font: {
      family: "InterMedium",
      weight: {
        4: "500",
      },
    },
  }),
  regular: createSystemFont({
    font: {
      family: "InterRegular",
      weight: {
        4: "400",
      },
    },
  }),
  heading: createSystemFont({
    font: {
      family: "InterMedium",
      weight: {
        4: "500",
      },
    },
  }),
  semibold: createSystemFont({
    font: {
      family: "InterSemiBold",
      weight: {
        4: "600",
      },
    },
  }),
};
