import { useNavigation } from "expo-router";
import { useEffect } from "react";
import { BackHandler } from "react-native";

export const usePreventBack = (condition = true, showHeaderLeft = false) => {
  const navigation = useNavigation();

  useDisableBackGestures(condition);

  useEffect(() => {
    if (condition) {
      navigation.setOptions({
        ...(showHeaderLeft ? {} : { headerLeft: () => null }),
        gestureEnabled: false,
      });

      navigation.getParent()?.setOptions({ gestureEnabled: false });

      const unsubscribeNavigationListener = navigation.addListener(
        "beforeRemove",
        (e) => {
          if (e.data.action.type === "POP") {
            // Prevent default behavior of leaving the screen
            e.preventDefault();
          }
        },
      );

      // Android back button handler
      const hardwareBackPressHandler = BackHandler.addEventListener(
        "hardwareBackPress",
        () => {
          // Prevent default behavior of leaving the screen
          return true;
        },
      );

      return () => {
        hardwareBackPressHandler.remove();
        unsubscribeNavigationListener();
      };
    }
  }, [condition, navigation, showHeaderLeft]);
};

/**
 * Disable swipe back gestures
 * It is important to do it for all parents of the screen
 * Otherwise, the swipe back gesture will be enabled by one of the parents
 * See https://github.com/expo/expo/issues/31614
 */
export function useDisableBackGestures(condition: boolean) {
  const navigation = useNavigation();

  useEffect(() => {
    if (!condition) {
      return;
    }
    // disable swipe
    let parent = navigation.getParent();
    while (parent) {
      parent.setOptions({ gestureEnabled: false });
      parent = parent.getParent();
    }

    // re-enable swipe after going back
    return () => {
      let currentParent = navigation.getParent();
      while (currentParent) {
        currentParent.setOptions({ gestureEnabled: true });
        currentParent = currentParent.getParent();
      }
    };
  }, [navigation, condition]);
}
