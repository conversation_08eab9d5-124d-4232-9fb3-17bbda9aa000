import { atomWithStorage } from "@utils/atomWithStorage";
import { useAtom } from "jotai";
import { useEffect } from "react";
import { useColorScheme } from "react-native";

type Theme = "light" | "dark";

const themeAtom = atomWithStorage<Theme | null>("theme", null);

export const useTheme = () => {
  const colorScheme = useColorScheme() as Theme;

  const [theme, setTheme] = useAtom(themeAtom);

  // Fallback to system color scheme
  useEffect(() => {
    if (theme === null) {
      setTheme(colorScheme);
    }
  }, [colorScheme, theme, setTheme]);

  const toggleTheme = () =>
    setTheme((prev) => (prev === "light" ? "dark" : "light"));

  return {
    theme: theme || colorScheme,
    isDarkTheme: theme === "dark",
    isLightTheme: theme === "light",
    toggleTheme,
  };
};
