import { BACKGROUNDS, ICONS } from "@constants/assets";
import { useTheme } from "@hooks/useTheme";

export const useAssets = () => {
  const { isDarkTheme } = useTheme();

  const icons = {
    appIcon: isDarkTheme ? ICONS.appIconDark : ICONS.appIconLight,
    salesforce: ICONS.salesforce,
  };

  const backgrounds = {
    default: isDarkTheme
      ? BACKGROUNDS.darkBackground
      : BACKGROUNDS.lightBackground,
    header: isDarkTheme
      ? BACKGROUNDS.headerDarkBackground
      : BACKGROUNDS.headerLightBackground,
  };

  return { icons, backgrounds };
};
