{
  "cli": {
    // https://docs.expo.dev/build-reference/app-versions/#syncing-already-defined-versions-to-remote
    "appVersionSource": "remote"
  },
  "build": {
    "base": {
      "cache": {
        "key": "expo-53-0-7"
      },
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api-staging.heypearl.ai"
      }
    },
    "development-simulator": {
      "channel": "development",
      "extends": "base",
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      },
      "ios": {
        "simulator": true
      },
      "env": {
        "EXPO_PUBLIC_APP_FLAVOR": "development"
      }
    },
    "development-device": {
      "channel": "development",
      "extends": "base",
      "developmentClient": true,
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      },
      "ios": {
        "simulator": false,
        "enterpriseProvisioning": "adhoc"
      },
      "env": {
        "EXPO_PUBLIC_APP_FLAVOR": "development"
      }
    },
    "preview": {
      "channel": "preview",
      "extends": "base",
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      },
      "ios": {
        "enterpriseProvisioning": "adhoc"
      },
      "env": {
        "EXPO_PUBLIC_APP_FLAVOR": "preview"
      }
    },
    "production": {
      "channel": "production",
      "extends": "base",
      "distribution": "store",
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_APP_FLAVOR": "production"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "6747335938",
        "appleTeamId": "5UM9YPASMF",
        "sku": "pearl.app",
        "language": "en"
      }
    }
  }
}
