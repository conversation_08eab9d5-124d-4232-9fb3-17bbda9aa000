# 🚀 Build & Deployment Guide

This guide explains the different build types, provisioning profiles, and deployment processes for the Pearl mobile app.

## 📋 Overview

Pearl uses three distinct build environments, each with its own configuration, bundle identifiers, and purpose:

| Environment     | App Name      | iOS Bundle ID              | Android Package            | Purpose                                  |
| --------------- | ------------- | -------------------------- | -------------------------- | ---------------------------------------- |
| **Development** | Pearl Dev     | `com.heypearl.app.dev`     | `com.heypearl.app.dev`     | Local development and testing            |
| **Preview**     | Pearl Preview | `com.heypearl.app.preview` | `com.heypearl.app.preview` | Internal testing and stakeholder reviews |
| **Production**  | Pearl         | `com.heypearl.app`         | `com.heypearl.app`         | Public app store releases                |

## Credentials

Everything is managed by Expo. You can find the credentials in the [Expo Credentials](https://expo.dev/accounts/heypearl/projects/pearl/credentials) page.

## 🎯 Environment Configuration

The app automatically configures itself based on the `EXPO_PUBLIC_APP_FLAVOR` environment variable:

```typescript
// Set in your environment or .env file
EXPO_PUBLIC_APP_FLAVOR = development | preview | production;
```

The configuration is done in the `app.config.ts` file.

eas.json is used to configure the build process.

For more information, see [Multiple App Variants](https://docs.expo.dev/tutorial/eas/multiple-app-variants/)

### Visual Environment Indicators

Each environment has visual indicators to help identify which build you're running:

- **Development**: Purple overlay (`#7B61FF50`) with "DEV" label
- **Preview**: Green overlay (`#00FF0050`) with "PREVIEW" label
- **Production**: No overlay or label

## 🍎 iOS Provisioning Profiles

### What are Provisioning Profiles?

Provisioning profiles are certificates that link your app, developer account, and devices. They tell iOS which apps can run on which devices and with what capabilities.

### Profile Types by Environment

#### Development Profile (`com.heypearl.app.dev`)

- **Profile type**: Ad Hoc
- **Purpose**: Local development and testing
- **Device Limit**: Up to 100 registered devices
- **Installation**: Via Expo & EAS Build
- **Validity**: 1 year
- **Capabilities**: All development features enabled

#### Preview Profile (`com.heypearl.app.preview`)

- **Profile type**: Ad Hoc
- **Purpose**: Internal testing and stakeholder reviews
- **Device Limit**: Up to 100 registered devices (TestFlight: 10,000)
- **Installation**: Via Expo & EAS Build
- **Validity**: 1 year
- **Capabilities**: Staging environment

#### Production Profile (`com.heypearl.app`)

- **Profile type**: App Store
- **Purpose**: App Store distribution
- **Device Limit**: Unlimited (through App Store)
- **Installation**: TestFlight & App Store
- **Validity**: 1 year
- **Capabilities**: Store-approved features only

## 🤖 Android Build Configuration

### Keystore Management

Android uses keystores instead of provisioning profiles. Each environment can use the same keystore but with different package names.

#### Keystore Types by Environment

| Environment | Package Name               | Keystore         | Purpose            |
| ----------- | -------------------------- | ---------------- | ------------------ |
| Development | `com.heypearl.app.dev`     | Debug keystore   | Local development  |
| Preview     | `com.heypearl.app.preview` | Release keystore | Internal testing   |
| Production  | `com.heypearl.app`         | Release keystore | Play Store release |

## 🏗️ Build Types Explained

### 1. Development Builds

**Purpose**: Active development and debugging

**Characteristics**:

- Includes development tools and debugging features
- Hot reload and fast refresh enabled
- Larger bundle size due to debug symbols
- Can connect to local development servers

**Installation**: Via Expo & EAS Build

### 2. Preview Builds

**Purpose**: Internal testing, stakeholder reviews, and QA

**Characteristics**:

- Production-like performance
- Optimized bundle size
- Can include additional logging for debugging
- Access to preview/staging backend services

**Installation**: Via Expo & EAS Build

### 3. Production Builds

**Purpose**: Public app store releases

**Characteristics**:

- Fully optimized for performance
- Minimal bundle size
- No development/debugging features
- Connects to production backend services

**Installation Methods**: Via Testflight & Stores

## Create a new build

You can build the app on EAS using the following commands:

```bash
# Development Device
pnpm build:ios:device
pnpm build:android:device

# Development Simulator
pnpm build:ios:simulator
pnpm build:android:simulator

# Preview
pnpm build:ios:preview
pnpm build:android:preview

# Production
pnpm build:ios:production
pnpm build:android:production
```

You can also build locally using the following option: `--local`

FYI, Android simulator builds can be installed on the device.

## 📱 Installation Guide

- [Internal Distribution Builds](https://docs.expo.dev/tutorial/eas/internal-distribution-builds/)

### iOS

- [iOS Development Build for devices](https://docs.expo.dev/tutorial/eas/ios-development-build-for-devices/)
- [iOS Development Build for simulators](https://docs.expo.dev/tutorial/eas/ios-development-build-for-simulators/)
- [iOS Production Build](https://docs.expo.dev/tutorial/eas/ios-production-build/)

### Android

- [Android Development Build](https://docs.expo.dev/tutorial/eas/android-development-build/)
- [Android Production Build](https://docs.expo.dev/tutorial/eas/android-production-build/)

### Getting Help

- **Expo Documentation**: [docs.expo.dev](https://docs.expo.dev)
- **EAS Build Docs**: [docs.expo.dev/build/introduction](https://docs.expo.dev/build/introduction)
- **Community Forum**: [forums.expo.dev](https://forums.expo.dev)
- **Discord**: [chat.expo.dev](https://chat.expo.dev)

## 📚 Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Expo Guides](https://docs.expo.dev/tutorial/eas/configure-development-build/)
- [Code Signing](https://www.freecodecamp.org/news/apple-code-signing-handbook/)
