import i18n from "@utils/i18n";

export const formatDateTime = (
  value: Date | string,
  mode: "date" | "time" | "datetime" | "longdate" | "longdatetime" | "timer",
): string => {
  const date = new Date(value);
  let options: Intl.DateTimeFormatOptions = {};

  switch (mode) {
    case "time":
      options = { hour: "2-digit", minute: "2-digit" };
      break;
    case "timer":
      options = { timeStyle: "long" };
      break;
    case "datetime":
      options = {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      };
      break;
    case "longdate":
      options = {
        year: "numeric",
        month: "long",
        day: "2-digit",
      };
      break;
    case "longdatetime":
      options = {
        year: "numeric",
        month: "long",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      };
      break;
    default:
      options = { year: "numeric", month: "2-digit", day: "2-digit" };
  }

  return new Intl.DateTimeFormat(i18n.language, options).format(date);
};
