import baseConfig from "@pearl/eslint-config/base";
import reactNativeConfig from "@pearl/eslint-config/react-native";

/** @type {import('typescript-eslint').Config} */
export default [
  {
    ignores: [
      ".expo/**",
      "ios/",
      "android/",
      "node_modules/",
      "build/",
      "dist/",
    ],
  },
  ...baseConfig,
  ...reactNativeConfig,
  {
    rules: {
      "@typescript-eslint/no-non-null-assertion": "warn",
      "react-hooks/exhaustive-deps": "warn",
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/require-await": "warn",
      "react/no-unstable-nested-components": "warn",
      "react-native/no-color-literals": "warn",
      "@typescript-eslint/no-restricted-imports": [
        "error",
        {
          paths: [
            {
              name: "tamagui",
              importNames: ["useTheme"],
              message: "Use @hooks/useTheme instead",
            },
            {
              name: "react-native",
              importNames: ["Linking"],
              message: "Use expo-linking instead",
            },
            {
              name: "react",
              importNames: ["default"],
              message:
                "Prefer destructuring imports to avoid cluttering the code.",
            },
            {
              name: "expo-linking",
              importNames: ["default"],
              message:
                "Don't import default from expo-linking. Import via destructured imports instead.",
            },
            {
              name: "@react-navigation/core",
              importNames: ["useTheme"],
              message:
                "Don't import useTheme from @react-navigation/core. Use @hooks/useTheme instead.",
            },
            {
              name: "i18next",
              importNames: ["t"],
              message:
                "Please use useTranslation hook from react-i18next instead of importing t directly from i18next.",
            },
          ],
        },
      ],
    },
  },
];
