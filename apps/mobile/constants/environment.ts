// Application environment types
export type AppFlavor = "development" | "preview" | "production";

// Current application environment
export const APP_FLAVOR = process.env.EXPO_PUBLIC_APP_FLAVOR as AppFlavor;

// API URL for current environment
export const API_URL = process.env.EXPO_PUBLIC_API_URL as string;

// Utilities to determine environment
export const IS_DEVELOPMENT = APP_FLAVOR === "development";

export const IS_PREVIEW = APP_FLAVOR === "preview";

export const IS_PRODUCTION = APP_FLAVOR === "production";

// Visual identification colors according to environment
export const ENV_COLORS = {
  development: "#7B61FF50", // Purple for dev
  preview: "#00FF0050", // Green for preview
  production: "", // No color in production
};

// Get current environment color
export const getCurrentEnvColor = (): string => ENV_COLORS[APP_FLAVOR];

// Environment labels for display
export const ENV_LABELS = {
  development: "DEV",
  preview: "PREVIEW",
  production: "", // No label in production
};

// Get current environment label
export const getCurrentEnvLabel = (): string => ENV_LABELS[APP_FLAVOR];
