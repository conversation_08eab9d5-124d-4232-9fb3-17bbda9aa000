---
description: 
globs: *.tsx,*.ts
alwaysApply: false
---
---
description: State management patterns
globs: "state/**/*.{ts,tsx}"
alwaysApply: true
---

- Use jotai for global atomic state
- Organize atoms by feature or domain
- Create derived atoms for computed state
- Use atom families for collections of similar state
- Prefer local component state when possible
- Use useReducer for complex local state
