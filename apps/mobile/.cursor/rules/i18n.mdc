---
description: 
globs: *.ts,*.tsx
alwaysApply: false
---
---
description: Internationalization standards
globs: "**/*.{ts,tsx}"
alwaysApply: true
---

- Use i18next & react-i18next for all user-facing text
- Support both French and English languages
- Never hardcode strings in components
- Use namespaces to organize translations
- Ensure all user-facing text is internationalized and supports localization.
