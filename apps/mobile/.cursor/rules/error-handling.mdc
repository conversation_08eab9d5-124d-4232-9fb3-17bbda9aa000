---
description: 
globs: *.ts,*.tsx
alwaysApply: false
---
---
description: Error handling patterns
globs: "**/*.{ts,tsx}"
alwaysApply: true
---

- Implement proper error logging using Sentry
- Handle errors at the beginning of functions
- Use early returns for error conditions
- Avoid unnecessary else statements; use if-return pattern instead
- Use error boundaries to catch unexpected errors
- Implement try/catch blocks for async operations
- Use expo-error-reporter for production errors
