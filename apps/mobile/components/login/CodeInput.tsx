import type { ComponentProps } from "react";
import { forwardRef } from "react";
import { Keyboard, TextInput } from "react-native";
import {
  CodeField,
  Cursor,
  useClearByFocusCell,
} from "react-native-confirmation-code-field";
import { getTokens, Text, View } from "tamagui";

type RNCodeFieldProps = ComponentProps<typeof CodeField>;

interface CodeInputProps extends Omit<RNCodeFieldProps, "renderCell"> {
  setValue: (value: string) => void;
  value: string;
  cellHeight?: number;
  cellWidth?: number;
  isPassword?: boolean;
}

export const CodeInput = forwardRef<TextInput, CodeInputProps>(
  function CodeInput(
    {
      value,
      setValue,
      showSoftInputOnFocus = true,
      cellCount = 6,
      isPassword,
      cellHeight = 44,
      cellWidth = 44,
      autoFocus = true,
      rootStyle,
      ...props
    },
    ref,
  ) {
    const [codeFieldProps, getCellOnLayoutHandler] = useClearByFocusCell({
      value,
      setValue,
    });

    const getSymbol = (symbol: string, isFocused: boolean) => {
      if (isFocused) {
        return <Cursor />;
      } else if (isPassword) {
        return "•";
      }
      return symbol;
    };

    return (
      <CodeField
        {...codeFieldProps}
        ref={ref}
        showSoftInputOnFocus={showSoftInputOnFocus}
        value={value}
        onChangeText={setValue}
        cellCount={cellCount}
        keyboardType="number-pad"
        InputComponent={TextInput}
        autoFocus={autoFocus}
        renderCell={({ index, symbol, isFocused }) => (
          <View
            key={index}
            onLayout={getCellOnLayoutHandler(index)}
            flex={1}
            height={cellHeight}
            width={cellWidth}
            borderRadius={8}
            borderWidth={1}
            borderColor={
              isFocused ? "$codeInputFocusedBorder" : "$codeInputBorder"
            }
            backgroundColor="$codeInputBackground"
            justifyContent="center"
          >
            <Text textAlign="center">{getSymbol(symbol, isFocused)}</Text>
          </View>
        )}
        onSubmitEditing={Keyboard.dismiss}
        rootStyle={[
          {
            columnGap: getTokens().space.$sm.val,
          },
          rootStyle,
        ]}
        {...props}
      />
    );
  },
);
