import { useTheme } from "@hooks/useTheme";
import type { InputProps } from "tamagui";
import { Input, View } from "tamagui";

export const LoginInput = (props: InputProps) => {
  const { isLightTheme } = useTheme();

  return (
    <View
      shadowColor="$black10"
      shadowOffset={{ width: 0, height: 1 }}
      shadowRadius={1}
      paddingHorizontal={isLightTheme ? 1 : undefined}
    >
      <Input
        height={40}
        borderRadius={8}
        borderWidth={1}
        paddingVertical="$md"
        borderColor="$loginInputBorder"
        paddingHorizontal="$lg"
        backgroundColor="$loginInputBackground"
        placeholderTextColor="$transparent60"
        keyboardAppearance={isLightTheme ? "light" : "dark"}
        {...props}
      />
    </View>
  );
};
