// TODO: Seems to conflict with tamagui
/* eslint-disable react-native/no-color-literals */
import type { ButtonProps } from "tamagui";
import { Button } from "tamagui";

export const LoginButton = (props: ButtonProps) => {
  return (
    <Button
      backgroundColor="$loginButtonBackground"
      borderColor="$loginButtonBorder"
      height={40}
      borderRadius={8}
      pressStyle={{
        backgroundColor: "$loginButtonBackground",
        scale: 0.98,
      }}
      color="$white"
      {...props}
    />
  );
};
