import {
  getCurrentEnvColor,
  getCurrentEnvLabel,
  IS_PRODUCTION,
} from "@constants/environment";
import { Text, View } from "tamagui";

/**
 * Composant qui affiche un indicateur de l'environnement actuel (dev, preview)
 * Ne s'affiche pas en production
 */
export const EnvIndicator = () => {
  // En production, on n'affiche pas l'indicateur
  if (IS_PRODUCTION) {
    return null;
  }

  return (
    <View
      position="absolute"
      top={0}
      right={0}
      zIndex={999}
      paddingRight={40}
      paddingLeft={20}
      paddingVertical={8}
      borderBottomLeftRadius={12}
      borderTopLeftRadius={0}
      borderTopRightRadius={0}
      style={{
        backgroundColor: getCurrentEnvColor(),
      }}
      pointerEvents="none"
    >
      <Text color="white" fontWeight="bold">
        {getCurrentEnvLabel()}
      </Text>
    </View>
  );
};
