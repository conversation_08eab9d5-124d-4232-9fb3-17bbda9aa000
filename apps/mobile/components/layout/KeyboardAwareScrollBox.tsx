import type { ComponentProps } from "react";
import { forwardRef } from "react";
import type { ScrollView } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";

type KeyboardAwareScrollBoxProps = ComponentProps<
  typeof KeyboardAwareScrollView
>;

export const KeyboardAwareScrollBox = forwardRef<
  ScrollView,
  KeyboardAwareScrollBoxProps
>(({ children, ...props }, ref) => (
  <KeyboardAwareScrollView
    style={{ flex: 1 }}
    showsVerticalScrollIndicator={false}
    keyboardShouldPersistTaps="handled"
    ref={ref}
    {...props}
  >
    {children}
  </KeyboardAwareScrollView>
));
