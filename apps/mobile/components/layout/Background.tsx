import { Image } from "@components/ui/Image";
import type { ImageProps } from "expo-image";
import { StyleSheet } from "react-native";
import { View } from "tamagui";

export const Background = ({
  backgroundImageSource,
}: {
  backgroundImageSource?: ImageProps["source"];
}) => {
  return (
    <View style={{ ...StyleSheet.absoluteFillObject }} pointerEvents="none">
      <Image
        source={backgroundImageSource}
        flex={1}
        contentFit="cover"
        // Fixes the issue with flickering background image
        placeholder={null}
        cachePolicy="memory-disk"
      />
    </View>
  );
};
