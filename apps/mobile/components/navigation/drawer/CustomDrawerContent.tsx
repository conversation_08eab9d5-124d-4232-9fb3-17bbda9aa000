import { Layout } from "@components/layout/Layout";
import { CustomDrawerRoutesList } from "@components/navigation/drawer/CustomDrawerRoutesList";
import { ThreadsList } from "@components/navigation/drawer/ThreadsList";
import { Icon } from "@components/ui/Icon";
import { useAssets } from "@hooks/useAssets";
import { useTheme } from "@hooks/useTheme";
import type { DrawerContentComponentProps } from "@react-navigation/drawer";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import {
  Avatar,
  Image,
  ScrollView,
  Spinner,
  Text,
  View,
  XStack,
} from "tamagui";

import { useGetUserAccounts } from "@pearl/api-hooks";

type CustomDrawerContentProps = DrawerContentComponentProps;

const ICON_SIZE = 24;

export const CustomDrawerContent = (props: CustomDrawerContentProps) => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const { t } = useTranslation(["components"]);
  const { isDarkTheme } = useTheme();
  const { icons } = useAssets();

  const { data: accounts, isLoading: isLoadingAccounts } = useGetUserAccounts();

  const [footerHeight, setFooterHeight] = useState(0);

  // TODO: add colors to theme when we have a design system
  const fadeGradientColors = isDarkTheme
    ? (["#1A1B1F00", "#1A1B1F"] as const)
    : (["#F0EFF600", "#F0EFF6"] as const);

  return (
    <Layout
      withBackground={false}
      backgroundColor="$drawerBackground"
      withHorizontalPadding={false}
      borderRightWidth={1}
      borderColor="$drawerBorder"
    >
      <XStack
        gap="$md"
        alignItems="center"
        paddingVertical="$lg"
        paddingHorizontal={28}
        marginBottom="$lg"
      >
        <Image
          source={icons.appIcon}
          style={{ width: ICON_SIZE, height: ICON_SIZE }}
        />
        <Text fontWeight="500" fontSize={14} lineHeight={20}>
          {"Pearl"}
        </Text>
      </XStack>
      <ScrollView
        contentContainerStyle={{
          gap: 12,
          paddingTop: 16,
          paddingLeft: 20,
          paddingRight: 30,
          paddingBottom: 200,
        }}
        showsVerticalScrollIndicator={false}
      >
        <CustomDrawerRoutesList {...props} />
        <Text fontSize={12} lineHeight={24} color="$infoText" marginTop="$3xl">
          {t("components:drawer.accounts")}
        </Text>
        <View gap="$xl">
          {isLoadingAccounts ? (
            <Spinner color="$grey" size="large" />
          ) : (
            accounts?.map((account) => (
              <XStack
                key={account.crmId}
                alignItems="center"
                gap="$lg"
                padding="$md"
                onPress={() => {
                  router.push({
                    pathname: "/chat",
                    params: {
                      accountId: account.crmId,
                    },
                  });
                }}
              >
                {/* TODO: add account icon when available */}
                <Icon name="SquareUser" color="$grey" size={20} />
                <Text
                  fontFamily="$regular"
                  fontSize={14}
                  lineHeight={20}
                  color="$grey"
                >
                  {account.crmName}
                </Text>
              </XStack>
            ))
          )}
        </View>
        <Text fontSize={12} lineHeight={24} color="$infoText" marginTop="$xl">
          {t("components:drawer.recent_conversations")}
        </Text>
        {accounts?.map((account) => (
          <ThreadsList key={account.crmId} account={account} />
        ))}
      </ScrollView>
      <LinearGradient
        style={{
          position: "absolute",
          bottom: insets.bottom + footerHeight,
          width: "100%",
          height: 56,
        }}
        colors={fadeGradientColors}
        locations={[0, 1]}
        pointerEvents="none"
      />
      <XStack
        alignItems="center"
        gap="$lg"
        paddingTop="$3xl"
        borderTopWidth={1}
        // TODO: add color to theme when we have a design system
        // TODO: Double check this
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        borderColor={isDarkTheme ? "#E0E0E00D" : "white"}
        paddingHorizontal={24}
        onLayout={(event) => {
          const { height } = event.nativeEvent.layout;
          setFooterHeight(height);
        }}
      >
        <Avatar>
          <Avatar.Image
            rounded="$full"
            source={{
              uri: "https://github.com/matt-west.png",
            }}
          />
        </Avatar>
        <View>
          <Text fontWeight="500" fontSize={14} lineHeight={20}>
            {"John Doe"}
          </Text>
          <Text fontWeight="400" fontSize={12} lineHeight={20} color="gray">
            {"<EMAIL>"}
          </Text>
        </View>
      </XStack>
    </Layout>
  );
};
