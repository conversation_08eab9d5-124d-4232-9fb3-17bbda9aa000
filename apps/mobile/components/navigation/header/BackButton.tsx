import { Icon } from "@components/ui/Icon";
import type { HeaderBackButtonProps } from "@react-navigation/elements";
import { useRouter } from "expo-router";
import type { GetThemeValueForKey } from "tamagui";
import { View } from "tamagui";

interface BackButtonProps extends HeaderBackButtonProps {
  color?: GetThemeValueForKey<"color">;
}

export const BackButton = ({ color, ...props }: BackButtonProps) => {
  const router = useRouter();

  const handlePress = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push("/");
    }
  };

  return (
    <View onPress={handlePress} {...props}>
      <Icon name="ArrowLeft" size={24} color={color} />
    </View>
  );
};
