// TODO: Fix this
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react/no-unstable-nested-components */
import { BackButton } from "@components/navigation/header/BackButton";
import { Image } from "@components/ui/Image";
import { useAssets } from "@hooks/useAssets";
import type { DrawerHeaderProps } from "@react-navigation/drawer";
import type {
  HeaderOptions,
  HeaderTitleProps as HeaderTitlePropsBase,
} from "@react-navigation/elements";
import {
  getHeaderTitle,
  Header as HeaderBase,
} from "@react-navigation/elements";
import type { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import type { TextProps } from "tamagui";
import { Text, View, XStack } from "tamagui";

import { useGetDefaultHeaderHeight } from "@/hooks/useGetDefaultHeaderHeight";

type HeaderTitleProps = Omit<TextProps, "style"> &
  Omit<HeaderTitlePropsBase, "style"> & {
    shouldAlignCenter?: boolean;
  };

export const HeaderTitle = ({
  shouldAlignCenter,
  ...props
}: HeaderTitleProps) => (
  <XStack
    alignItems="center"
    justifyContent={shouldAlignCenter ? "center" : "flex-start"}
    marginRight="$2xl"
    marginLeft="$xs"
  >
    <Text
      fontWeight="600"
      fontSize={20}
      lineHeight={28}
      numberOfLines={1}
      textAlign={shouldAlignCenter ? "center" : "left"}
      {...props}
    >
      {props.children}
    </Text>
  </XStack>
);

export type HeaderProps = HeaderOptions &
  (DrawerHeaderProps | NativeStackHeaderProps) & {
    isModal?: boolean;
    onBackPress?: () => void;
    withHeaderBackground?: boolean;
  };

export const Header = ({
  options,
  route,
  onBackPress,
  withHeaderBackground,
  isModal,
  ...props
}: HeaderProps) => {
  const insets = useSafeAreaInsets();
  const { defaultHeaderHeight } = useGetDefaultHeaderHeight(isModal);

  const { backgrounds } = useAssets();

  const hasBack = "back" in props && !!props.back;

  const headerLeft =
    (options.headerLeft as HeaderOptions["headerLeft"]) ??
    (hasBack || onBackPress
      ? (headerProps) =>
          onBackPress ? (
            <BackButton onPress={onBackPress} {...headerProps} />
          ) : (
            <BackButton {...headerProps} />
          )
      : undefined);

  return (
    <HeaderBase
      {...options}
      title={getHeaderTitle(options, route.name)}
      headerLeft={headerLeft}
      headerTitle={
        options.headerTitle
          ? options.headerTitle
          : (headerProps) => (
              <HeaderTitle
                shouldAlignCenter={!!options.headerTitleAlign}
                {...headerProps}
              />
            )
      }
      headerStyle={[
        {
          backgroundColor: "transparent",
        },
        options.headerStyle,
      ]}
      headerStatusBarHeight={insets.top}
      headerLeftContainerStyle={{ paddingStart: 12 }}
      headerRightContainerStyle={{ paddingEnd: 20 }}
      headerShadowVisible={false}
      headerBackground={
        withHeaderBackground
          ? () => (
              <View height={insets.top + defaultHeaderHeight}>
                <Image
                  source={backgrounds.header}
                  style={{ flex: 1 }}
                  contentFit="fill"
                />
              </View>
            )
          : undefined
      }
      {...props}
    />
  );
};
