import * as LucideIcons from "@tamagui/lucide-icons";
import type { GetThemeValueForKey, SizeTokens } from "tamagui";

export type IconProps = {
  name: keyof typeof LucideIcons;
  color?: GetThemeValueForKey<"color">;
  onPress?: () => void;
  size?: number | SizeTokens;
};

export const Icon = ({ name, color, size, onPress }: IconProps) => {
  const LucideIcon = LucideIcons[name];

  return (
    <LucideIcon color={color} size={size} onPress={onPress} hitSlop={20} />
  );
};
