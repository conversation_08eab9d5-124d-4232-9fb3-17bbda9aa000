import { Text, View } from "tamagui";

type ChatSuggestionProps = {
  description: string;
  onPress: () => void;
  title: string;
};

export const ChatSuggestion = ({
  title,
  description,
  onPress,
}: ChatSuggestionProps) => (
  <View
    paddingHorizontal="$2xl"
    paddingVertical="$lg"
    backgroundColor="$chatBarSuggestionBackground"
    borderRadius={12}
    onPress={onPress}
    pressStyle={{
      scale: 0.98,
    }}
  >
    <Text fontFamily="$semibold" fontSize={14} lineHeight={24} color="$grey">
      {title}
    </Text>
    <Text fontFamily="$regular" fontSize={14} lineHeight={24} color="$grey">
      {description}
    </Text>
  </View>
);
