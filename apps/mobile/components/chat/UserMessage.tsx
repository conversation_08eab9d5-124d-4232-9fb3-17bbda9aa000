import type { UIMessage } from "ai";
import { Text, View } from "tamagui";

type UserMessageProps = {
  message: UIMessage;
};

export const UserMessage = ({ message }: UserMessageProps) => (
  <View
    backgroundColor="$chatUserMessageBackground"
    alignSelf="flex-end"
    paddingHorizontal="$xl"
    paddingVertical="$lg"
    borderRadius="$full"
  >
    <Text
      fontFamily="$regular"
      fontSize={16}
      lineHeight={24}
      color="$grey"
      letterSpacing={-0.16}
    >
      {message.content}
    </Text>
  </View>
);
