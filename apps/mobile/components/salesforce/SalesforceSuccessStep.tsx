import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import { <PERSON><PERSON>, Spinner, Text, YStack } from "tamagui";

import { useGetUserCRM } from "@pearl/api-hooks";

import { Icon } from "@/components/ui/Icon";

export const SalesforceSuccessStep = () => {
  const router = useRouter();
  const { t } = useTranslation(["components"]);
  const { refetch: refreshUserCRM } = useGetUserCRM();

  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const checkScale = useSharedValue(0);

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Sequence of animations for smooth entrance
    scale.value = withSpring(1, { damping: 15, stiffness: 100 });
    opacity.value = withTiming(1, { duration: 800 });

    // Delayed check mark animation
    setTimeout(() => {
      checkScale.value = withSequence(
        withSpring(1.2, { damping: 10 }),
        withSpring(1, { damping: 15 }),
      );
    }, 300);
  }, [scale, opacity, checkScale]);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const animatedCheckStyle = useAnimatedStyle(() => ({
    transform: [{ scale: checkScale.value }],
  }));

  const handleOpenPearl = async () => {
    setIsLoading(true);
    try {
      await refreshUserCRM();
      setTimeout(() => {
        router.push("/salesforce");
      }, 2000);
    } catch (error) {
      //TODO: handle error
      console.error(error);
    }
  };

  if (isLoading) {
    return <Spinner size="large" color="$grey" />;
  }

  return (
    <Animated.View style={animatedContainerStyle}>
      <YStack alignItems="center" gap="$lg" paddingHorizontal="$lg">
        <YStack alignItems="center" marginBottom="$md">
          <Animated.View style={animatedCheckStyle}>
            <Icon name="CheckCircle" size={64} color="$green10" />
          </Animated.View>
        </YStack>

        <Text
          fontSize={28}
          fontWeight="700"
          textAlign="center"
          color="$grey"
          marginBottom="$md"
        >
          {t("components:salesforce.successStep.title")}
        </Text>

        <Text
          fontSize={16}
          lineHeight={24}
          textAlign="center"
          color="$infoText"
          marginBottom="$xl"
          maxWidth={400}
        >
          {t("components:salesforce.successStep.description")}
        </Text>

        <Button
          backgroundColor="$loginButtonBackground"
          borderColor="$loginButtonBorder"
          borderWidth={1}
          height={48}
          borderRadius={8}
          paddingHorizontal="$2xl"
          onPress={handleOpenPearl}
          pressStyle={{
            scale: 0.98,
          }}
          width={300}
        >
          <Button.Text color="$white" fontSize={16} fontWeight="500">
            {t("components:salesforce.successStep.cta")}
          </Button.Text>
        </Button>
      </YStack>
    </Animated.View>
  );
};
