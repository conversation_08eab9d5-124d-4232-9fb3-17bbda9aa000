# 📱 Pearl - Mobile App

iOS/Android application for Pearl, developed with Expo and React Native.

## 🚀 Getting Started

### Requirements

Before you begin, make sure you have the proper development environment set up:

**👉 [Set up your development environment](https://docs.expo.dev/get-started/set-up-your-environment/?platform=ios&device=simulated&mode=development-build)**

Follow Expo's official guide to configure your system for iOS and/or Android development.

### Installing pnpm

This project uses `pnpm` as the package manager.

See [pnpm installation](https://pnpm.io/installation)

### Project Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/heypearl-ai/pearl-frontend.git
   cd pearl-frontend
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Start the development server**
   ```bash
   cd apps/mobile
   pnpm start
   ```

## 📱 Running the App

### iOS Simulator

1. **Create a development build**

   ```bash
   pnpm build:ios:simulator
   ```

2. **Start the development server**

   ```bash
   pnpm start
   ```

3. **Launch the app**
   - The app will automatically open in the iOS Simulator
   - Or press `i` in the terminal to open iOS Simulator

### iOS Device

1. **Add your device to the provisioning profile**

   ```bash
   eas device:create
   ```

2. **Create a development build**

   ```bash
   pnpm build:ios:device
   ```

3. **Start the development server**

   ```bash
   pnpm start
   ```

4. **Launch the app**
   - Make sure your device is connected to the same network as your Mac
   - Install the development build on your device
   - Launch the app from your device

### Android Emulator

1. **Start Android Emulator**

   - Open Android Studio
   - Start an Android Virtual Device (AVD)

2. **Create a development build**

   ```bash
   pnpm android
   ```

3. **Start the development server**

   ```bash
   pnpm start
   ```

4. **Launch the app**
   - The app will automatically install and open on the emulator
   - Or press `a` in the terminal to open Android emulator

### Android Device

1. **Connect your device**

   ```bash
   # Verify device is detected
   adb devices
   ```

2. **Enable Developer Options and USB Debugging on your device**

3. **Create a development build**

   ```bash
   pnpm android
   ```

4. **Start the development server**

   ```bash
   pnpm start
   ```

5. **Launch the app**
   - The app will automatically install and open on your device

## 🔧 Development

### Available Scripts

- `pnpm start` - Start the Expo development server
- `pnpm ios` - Build and run on iOS simulator/device
- `pnpm android` - Build and run on Android emulator/device
- `pnpm test` - Run tests
- `pnpm lint` - Run linting
- `pnpm type-check` - Run TypeScript type checking

### Development Tools

- **Hot Reload**: Code changes are automatically reflected in the app
- **Fast Refresh**: React components reload without losing state
- **Debug Menu**: Shake your device or press `Cmd+D` (iOS) / `Cmd+M` (Android)

## 🐛 Debugging

The best way to debug is using the [Expo debugger and other debugging tools](https://docs.expo.dev/debugging/tools/):

- Press `j` in the terminal to open the debugger
- Use React Native DevTools for component inspection
- Enable remote debugging for JavaScript debugging
- Use React Query DevTools for debugging

### Common Debug Commands

- `j` - Open debugger
- `r` - Reload the app
- `d` - Open developer menu
- `i` - Open iOS simulator
- `a` - Open Android emulator

### Build Environments

Pearl supports three distinct build environments, each with visual indicators:

- **Development** (`Pearl Dev`): Purple overlay with "DEV" label - for local development
- **Preview** (`Pearl Preview`): Green overlay with "PREVIEW" label - for internal testing
- **Production** (`Pearl`): No overlay - for app store releases

Each environment uses different bundle identifiers and package names to allow side-by-side installation.

**👉 For detailed build and deployment information, see [Build & Deployment Guide](docs/build-and-deployment.md)**

## 🤖 AI-Powered Development

We use [Cursor](https://cursor.sh/) as our IDE with AI assistance:

- **Configuration**: See [`.cursorrules`](.cursorrules) for AI guidelines
- **Privacy Mode**: Enable in Cursor > Preferences > Privacy Mode to keep code local
- **Best Practices**: Update `.cursorrules` when AI makes incorrect suggestions

## 📚 Documentation

### Project Documentation

- **[Build & Deployment](docs/build-and-deployment.md)** - Complete guide to builds, provisioning profiles, and deployment
- **[Release Process](docs/release-process.md)** - How to create and deploy releases
- **[Adding New Languages](docs/adding-new-language.md)** - Internationalization guide

### Key Technologies

- **[React Native](https://reactnative.dev/docs/environment-setup)** - Mobile app framework
- **[Expo](https://docs.expo.dev/)** - Development platform and tools
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[i18next](https://www.i18next.com/)** - Internationalization framework

## 🚀 Build & Deployment

Pearl uses a multi-environment build system with three distinct configurations:

| Environment | Purpose           | Bundle ID                  | Visual Indicator        |
| ----------- | ----------------- | -------------------------- | ----------------------- |
| Development | Local development | `com.heypearl.app.dev`     | Purple "DEV" overlay    |
| Preview     | Internal testing  | `com.heypearl.app.preview` | Green "PREVIEW" overlay |
| Production  | App store release | `com.heypearl.app`         | None                    |

**👉 See [Build & Deployment Guide](docs/build-and-deployment.md)** for comprehensive information about:

- iOS provisioning profiles and certificates
- Android keystore management
- Build types and installation methods
- EAS Build configuration
- Troubleshooting common issues

**👉 See [Release Process Documentation](docs/release-process.md)** for step-by-step release procedures

## 🌍 Internationalization

To add support for new languages:

**👉 See [Adding New Languages Documentation](docs/adding-new-language.md)**

Currently supported languages:

- English (en) - Default
- French (fr)

## 📁 Project Structure

```
apps/mobile/
├── app/                    # Expo Router pages
├── components/             # Reusable UI components
├── constants/              # App constants
├── hooks/                  # Custom React hooks
├── locales/                # Language files (symlinked from packages/locales)
├── providers/              # Context providers
├── services/               # API services
├── state/                  # State management
├── theme/                  # Design system and themes
├── types/                  # TypeScript type definitions
├── utils/                  # Utility functions
└── docs/                   # Project documentation
```
