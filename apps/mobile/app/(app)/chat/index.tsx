import { AssistantMessage } from "@components/chat/AssistantMessage";
import { ChatBarInput } from "@components/chat/ChatBarInput";
import { ChatSuggestionsList } from "@components/chat/ChatSuggestionsList";
import { UserMessage } from "@components/chat/UserMessage";
import { KeyboardAwareScrollBox } from "@components/layout/KeyboardAwareScrollBox";
import { Layout } from "@components/layout/Layout";
import * as Crypto from "expo-crypto";
import { useLocalSearchParams, useNavigation } from "expo-router";
import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import type { ScrollView } from "react-native";
import { KeyboardStickyView } from "react-native-keyboard-controller";
import { FadeIn } from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Spinner, Text, View } from "tamagui";

import {
  useChatStream,
  useGetAccountThreads,
  useGetThreadMessages,
  useGetUserAccounts,
} from "@pearl/api-hooks";

import { PulsingCircle } from "@/components/chat/PulsingCircle";
import { AnimatedView } from "@/components/ui/AnimatedView";

// TODO:
// translations
// header right logic
// hide keyboard when scrolling
// add button to scroll to the last message
// add pulsing circle to the last assistant message when streaming

// TODO: get this from the backend
const MOCK_SUGGESTIONS = [
  {
    title: "Suggest next steps",
    description: "to help me close @deal",
  },
  {
    title: "Create a new report",
    description: "for my manager",
  },
  {
    title: "Draft a follow up",
    description: "for my customer",
  },
];

const getRandomThreadId = () => Crypto.randomUUID();

export default function AccountScreen() {
  const { t } = useTranslation(["screens"]);
  const navigation = useNavigation();
  const { bottom: bottomInset } = useSafeAreaInsets();

  const { accountId, threadId } = useLocalSearchParams<{
    prompt: string;
    accountId?: string;
    threadId?: string;
  }>();

  // TODO: Double check the type here
  const scrollRef = useRef<ScrollView>(null);

  const [localThreadId, setLocalThreadId] =
    useState<string>(getRandomThreadId());

  const [error, setError] = useState<string | null>(null);

  const { data: accounts, isLoading: isLoadingAccounts } = useGetUserAccounts();
  const { data: threadMessages, isLoading: isLoadingThreadMessages } =
    useGetThreadMessages(threadId);

  const selectedAccount = accounts?.find(
    (account) => account.crmId === accountId,
  );

  const selectedAccountId = selectedAccount?.crmId;

  const { refetch: refetchThreads } = useGetAccountThreads(selectedAccountId);

  const isLoading = isLoadingAccounts || isLoadingThreadMessages;

  const keyboardStickyViewClosedOffset =
    bottomInset === 0 ? -34 : Math.max(-bottomInset - 20, -34);

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    append,
    handleInputChange,
    stop,
    status: chatStatus,
  } = useChatStream({
    threadId: localThreadId,
    crmAccountId: selectedAccountId,
    onFinish: () => {
      if (localThreadId !== threadId) {
        // Add new thread to sidebar and select it
        void refetchThreads();
      }
    },
    onError: (err) => {
      console.error("Error in useChatStream", err);
      setError(err.message);
    },
  });

  useEffect(() => {
    if (threadId) {
      setLocalThreadId(threadId);
    } else {
      // If no thread is selected, generate a new thread ID
      setLocalThreadId(getRandomThreadId());
    }
  }, [threadId, setLocalThreadId]);

  useEffect(() => {
    if (threadMessages) {
      setMessages(threadMessages.messages);
    }
  }, [setMessages, threadMessages]);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messages.length > 0 && scrollRef.current) {
      setTimeout(() => {
        scrollRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: accounts?.find((account) => account.crmId === accountId)?.crmName,
    });
  }, [accounts, accountId, navigation]);

  return (
    <Layout
      withBackground={false}
      backgroundColor="$chatBackground"
      withHorizontalPadding={false}
      withEdgeBottom={false}
      paddingTop="$2xl"
    >
      {isLoading ? (
        <View flex={1} justifyContent="center" alignItems="center">
          <Spinner size="large" color="$grey" />
        </View>
      ) : (
        <AnimatedView entering={FadeIn.duration(500)} flex={1}>
          {/* TODO: Flashlist
          https://github.com/kirillzyusko/react-native-keyboard-controller/issues/888#issuecomment-**********
        */}
          <KeyboardAwareScrollBox
            ref={scrollRef}
            contentContainerStyle={{
              paddingHorizontal: 20,
              paddingBottom: 200,
              gap: 20,
            }}
          >
            {messages.map((message) =>
              message.role === "user" ? (
                <UserMessage
                  key={message.id + message.createdAt?.toISOString()}
                  message={message}
                />
              ) : (
                <AssistantMessage
                  key={message.id + message.createdAt?.toISOString()}
                  message={message}
                />
              ),
            )}
            {chatStatus === "submitted" ? <PulsingCircle /> : null}
            {error ? (
              <Text color="$chatError">{t("screens:chat.error_generic")}</Text>
            ) : null}
          </KeyboardAwareScrollBox>
          <KeyboardStickyView
            offset={{
              closed: keyboardStickyViewClosedOffset,
            }}
          >
            {input.length === 0 &&
            messages.length === 0 &&
            chatStatus === "ready" ? (
              <ChatSuggestionsList
                suggestions={MOCK_SUGGESTIONS}
                onSuggestionPress={(suggestion) =>
                  append({
                    role: "user",
                    content: suggestion.title + " " + suggestion.description,
                  })
                }
                contentContainerStyle={{
                  marginBottom: 28,
                  paddingHorizontal: 20,
                }}
              />
            ) : null}
            <ChatBarInput
              autoFocus
              value={input}
              onChange={(e) =>
                handleInputChange({
                  ...e,
                  target: {
                    ...e.target,
                    value: e.nativeEvent.text,
                  },
                } as unknown as React.ChangeEvent<HTMLInputElement>)
              }
              placeholder={t("screens:chat.placeholder")}
              returnKeyType="send"
              onSendMessage={handleSubmit}
              onSubmitEditing={(e) => {
                handleSubmit(e);
                e.preventDefault();
              }}
              onStop={stop}
              isStopButtonVisible={["submitted", "streaming"].includes(
                chatStatus,
              )}
            />
          </KeyboardStickyView>
        </AnimatedView>
      )}
    </Layout>
  );
}
