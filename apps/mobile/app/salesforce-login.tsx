import { useLocalSearchParams, useRouter } from "expo-router";
import { StyleSheet, useWindowDimensions } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import type { WebViewNavigation } from "react-native-webview";
import { WebView } from "react-native-webview";
import { Spinner, View } from "tamagui";

export default function SalesforceLoginScreen() {
  const router = useRouter();
  const { authUrl } = useLocalSearchParams<{ authUrl: string }>();

  const screen = useWindowDimensions();

  const onNavigationChange = (navState: WebViewNavigation) => {
    const url = new URL(navState.url);

    const params = new URLSearchParams(url.search);

    if (!navState.loading && params.has("code") && params.has("state")) {
      const code = params.get("code");
      const state = params.get("state");

      router.push({
        pathname: "/salesforce-callback",
        params: {
          code,
          state,
        },
      });
    }
  };

  const renderLoading = () => (
    <View
      position="absolute"
      height={screen.height}
      width={screen.width}
      top={0}
      paddingTop={12}
    >
      <Spinner size="large" color="$grey" />
    </View>
  );

  return (
    <SafeAreaView style={StyleSheet.absoluteFillObject}>
      <WebView
        source={{ uri: authUrl }}
        renderLoading={renderLoading}
        onNavigationStateChange={onNavigationChange}
      />
    </SafeAreaView>
  );
}
