import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { FadeIn } from "react-native-reanimated";
import { Button, Heading, Text, YStack } from "tamagui";

import { Layout } from "@/components/layout/Layout";
import { Image } from "@/components/ui/Image";
import { useAssets } from "@/hooks/useAssets";
import { usePreventBack } from "@/hooks/usePreventBack";

export default function OnboardingScreen() {
  const router = useRouter();
  const { t } = useTranslation(["screens"]);
  const { icons } = useAssets();

  usePreventBack();

  return (
    <Layout
      justifyContent="center"
      alignItems="center"
      gap="$2xl"
      entering={FadeIn.duration(500)}
    >
      <Image
        source={icons.appIcon}
        width={56}
        aspectRatio={1}
        alignSelf="center"
        marginBottom="$lg"
      />

      <Heading
        fontSize={28}
        fontWeight="700"
        textAlign="center"
        marginBottom="$md"
        color="$grey"
      >
        {t("screens:onboarding.title")}
      </Heading>

      <YStack gap="$md" alignItems="center" marginBottom="$2xl">
        <Text
          fontSize={16}
          lineHeight={24}
          textAlign="center"
          color="$infoText"
          maxWidth={400}
        >
          {t("screens:onboarding.description")}
        </Text>

        <Text
          fontSize={14}
          lineHeight={20}
          textAlign="center"
          color="$infoText"
          marginTop="$sm"
        >
          {t("screens:onboarding.description2")}
        </Text>
      </YStack>

      <Button
        backgroundColor="$loginButtonBackground"
        borderColor="$loginButtonBorder"
        borderWidth={1}
        height={48}
        borderRadius={8}
        paddingHorizontal="$2xl"
        onPress={() => router.push("/salesforce")}
        pressStyle={{
          scale: 0.98,
        }}
        width="$full"
      >
        <Button.Text color="$white" fontSize={16} fontWeight="500">
          {t("screens:onboarding.cta")}
        </Button.Text>
      </Button>
    </Layout>
  );
}
