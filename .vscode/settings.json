{"eslint.format.enable": true, "eslint.enable": true, "eslint.debug": true, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": [{"pattern": "apps/*"}, {"pattern": "packages/*"}, {"pattern": "tools/*"}], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript,javascriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript,typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.ignorePath": ".giti<PERSON>re", "editor.formatOnSave": true, "color-highlight.matchHslWithNoFunction": true, "editor.formatOnSaveMode": "modificationsIfAvailable", "javascript.preferences.useAliasesForRenames": false, "typescript.preferences.useAliasesForRenames": false, "i18n-ally.localesPaths": ["packages/locales/src"], "i18n-ally.enabledFrameworks": ["react", "vscode", "react-i18next", "i18next"], "i18n-ally.keystyle": "nested", "i18n-ally.namespace": true, "i18n-ally.displayLanguage": "en"}